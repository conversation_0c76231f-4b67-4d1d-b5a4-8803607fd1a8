from common.OpenClient import OpenClient


class Config:
    # 应用id
    app_id = ''
    # 应用私钥
    private_key = ''
    # 请求URL
    url = 'https://sbappstoreapi.ziniao.com'


def create_client():
    """
    可以以导入模块的方式获得一个单例对象
    Example:
    from rpa.OpenApiConfig import client
    response = client.execute(request)
    """
    # 应用id
    app_id = Config.app_id
    # 应用私钥
    private_key = Config.private_key
    # 请求URL
    url = Config.url
    # 创建请求客户端
    return OpenClient(app_id, private_key, url)


client = create_client()
