#!/usr/bin/python
# -*- coding: UTF-8 -*-
from common import <PERSON>son<PERSON><PERSON>
from model.RpaPlanListGetModel import RpaPlanListGetModel
from request.RpaPlanListGetRequest import RpaPlanListGetRequest
from common.OpenClient import OpenClient
from OpenApiConfig import Config


def rpa_plan_list_get():
    """查询公司下的计划列表"""
    # 创建请求客户端
    client = OpenClient(Config.app_id, Config.private_key, Config.url)

    # 创建请求
    request = RpaPlanListGetRequest()
    # 请求参数，方式一
    model = RpaPlanListGetModel()
    model.planStatus = 'ENABLED'
    model.platformId = 0
    model.name = '测试任务11'
    model.id = 5070
    request.biz_model = JsonUtil.to_json_string(model)
    # 请求参数，方式二
    # request.biz_model = {
    #     'planStatus': 'ENABLED',
    #     'platformId': 0,
    #     'name': '测试任务11'
    # }

    # 调用请求
    response = client.execute(request, app_token='填写app_token')

    if response.is_success():
        print('response: ', response)
        print('data: ', response.data)
    else:
        print("response: ", response)
        print('请求失败,request_id:%s, code:%s, msg:%s, sub_code:%s, sub_msg:%s' % \
              (response.request_id, response.code, response.msg, response.sub_code, response.sub_msg))


if __name__ == '__main__':
    rpa_plan_list_get()
