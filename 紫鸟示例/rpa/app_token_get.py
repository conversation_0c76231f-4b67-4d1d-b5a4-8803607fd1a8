#!/usr/bin/python
# -*- coding: UTF-8 -*-
from request.AppTokenRequest import AppTokenRequest
from common.OpenClient import OpenClient
from OpenApiConfig import Config


def rpa_plan_list_get():
    # 创建请求客户端
    client = OpenClient(Config.app_id, Config.private_key, Config.url)

    # 创建请求
    request = AppTokenRequest()

    # 调用请求
    response = client.execute(request)

    if response.is_success():
        print('response: ', response)
        print('data: ', response.data)
    else:
        print("response: ", response)
        print('请求失败,request_id:%s, code:%s, msg:%s, sub_code:%s, sub_msg:%s' % \
              (response.request_id, response.code, response.msg, response.sub_code, response.sub_msg))


if __name__ == '__main__':
    rpa_plan_list_get()
