#!/usr/bin/python
# -*- coding: UTF-8 -*-
from common import Json<PERSON><PERSON>
from model.RpaTasksGetModel import RpaTasksGetModel
from request.RpaTasksGetRequest import RpaTasksGetRequest
from common.OpenClient import OpenClient
from OpenApiConfig import Config


def rpa_tasks_get():
    # 创建请求客户端
    client = OpenClient(Config.app_id, Config.private_key, Config.url)

    # 创建请求
    request = RpaTasksGetRequest()
    # 请求参数，方式一
    model = RpaTasksGetModel()
    model.planId = 5070
    model.pageNum = 1
    model.pageSize = 10
    request.biz_model = JsonUtil.to_json_string(model)
    # 请求参数，方式二
    # request.biz_model = {
    #     'timeFrom': 1658478400000,
    #     'timeTo': 1658478404904,
    #     'planId': 5070,
    #     'pageNum': 1,
    #     'pageSize': 10
    # }

    # 调用请求
    response = client.execute(request, app_token='填写app_token')

    if response.is_success():
        print('response: ', response)
        print('data: ', response.data)
    else:
        print("response: ", response)
        print('请求失败,request_id:%s, code:%s, msg:%s, sub_code:%s, sub_msg:%s' % \
              (response.request_id, response.code, response.msg, response.sub_code, response.sub_msg))


if __name__ == '__main__':
    rpa_tasks_get()
