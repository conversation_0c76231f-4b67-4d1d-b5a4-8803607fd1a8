#!/usr/bin/python
# -*- coding: UTF-8 -*-
from common import JsonUtil
from model.RpaPlansReplacePostModel import RpaPlansReplacePostModel
from model.RpaCreateParamsModel import Script, RunWith, StoreAndMarketPlace
from request.RpaPlansReplacePostRequest import RpaPlansReplacePostRequest
from common.OpenClient import OpenClient
from OpenApiConfig import Config


def rpa_plans_replace_post():
    # 创建请求客户端
    client = OpenClient(Config.app_id, Config.private_key, Config.url)

    # 创建请求
    request = RpaPlansReplacePostRequest()
    # 请求参数，方式一
    model = RpaPlansReplacePostModel()
    model.planId = "5070"
    model.name = '任务名称repeat'
    model.platformId = '0'
    model.actionType = 'REPEAT_BY_WEEK'
    model.repeatRange = '2,3'
    model.repeatTimeAt = '00:01'
    # 添加rpa列表
    script1_run_with = RunWith('downloadTimeType', 'last60')
    run_with_list = [script1_run_with.__dict__]

    script1 = Script('1002', run_with_list)
    model.scriptList = [script1.__dict__]

    model.storeScopeType = 'ASSIGNED'
    # 添加店铺和站点
    store_and_market_place = StoreAndMarketPlace('16310663243655', 'US,ES')
    model.storeAndMarketPlaceList = [store_and_market_place.__dict__]

    request.biz_model = JsonUtil.to_json_string(model)

    # 请求参数，方式二
    # request.biz_model = {
    #     "planId": "5070",
    #     "name": "任务名称repeat",
    #     "platformId": "0",
    #     "actionType": "REPEAT_BY_WEEK",
    #     "repeatRange": "2,3",
    #     "repeatTimeAt": "00:01",
    #     "validFrom": None,
    #     "validTo": None,
    #     "scriptList": [
    #         {
    #             "scriptId": "1002",
    #             "runWith": [
    #                 {
    #                     "name": "downloadTimeType",
    #                     "value": "last60"
    #                 }
    #             ]
    #         }
    #     ],
    #     "storeScopeType": "ASSIGNED",
    #     "storeAndMarketPlaceList": [
    #         {
    #             "sellerIds": "16310663243655",
    #             "marketPlace": "US,ES"
    #         }
    #     ],
    #     "extInfo": {}  # JSON格式的自定义参数
    # }

    # 调用请求
    response = client.execute(request, app_token='填写app_token')

    if response.is_success():
        print('response: ', response)
        print('data: ', response.data)
    else:
        print("response: ", response)
        print('请求失败,request_id:%s, code:%s, msg:%s, sub_code:%s, sub_msg:%s' % \
              (response.request_id, response.code, response.msg, response.sub_code, response.sub_msg))


if __name__ == '__main__':
    rpa_plans_replace_post()
