#!/usr/bin/python
# -*- coding: UTF-8 -*-
from common import Json<PERSON><PERSON>
from model.RpaPlansDriversDelModel import RpaPlansDriversDelModel
from request.RpaPlansDriversDelRequest import RpaPlansDriversDelRequest
from common.OpenClient import OpenClient
from OpenApiConfig import Config


def rpa_plans_drivers_delete():
    """停用计划"""
    # 创建请求客户端
    client = OpenClient(Config.app_id, Config.private_key, Config.url)

    # 创建请求
    request = RpaPlansDriversDelRequest()
    # 请求参数，方式一
    model = RpaPlansDriversDelModel()
    model.planIds = '5070,5167'
    # 非GET请求, Query参数, 使用params_model
    request.params_model = JsonUtil.to_json_string(model)
    # 请求参数，方式二
    # request.params_model = {
    #     'planIds': '5070,5167'
    # }

    # 调用请求
    response = client.execute(request, app_token='填写app_token')

    if response.is_success():
        print('response: ', response)
        print('data: ', response.data)
    else:
        print("response: ", response)
        print('请求失败,request_id:%s, code:%s, msg:%s, sub_code:%s, sub_msg:%s' % \
              (response.request_id, response.code, response.msg, response.sub_code, response.sub_msg))


if __name__ == '__main__':
    rpa_plans_drivers_delete()
