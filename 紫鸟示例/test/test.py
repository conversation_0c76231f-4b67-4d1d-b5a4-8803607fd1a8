#!/usr/bin/python
# -*- coding: UTF-8 -*-
import unittest

from request.CompanyAllUserRequest import CompanyAllUserRequest
from common.OpenClient import OpenClient


class MyTestCase(unittest.TestCase):
    # 应用id
    app_id = ''
    # 应用私钥
    private_key = ''

    # 请求URL
    url = 'https://sbappstoreapi.ziniao.com'
    # 创建请求客户端
    client = OpenClient(app_id, private_key, url)

    def test_api(self):
        # 创建请求
        request = CompanyAllUserRequest()
        # 添加请求参数 方式一
        # model = MemberInfoPostModel()
        # model.userCode = "c4e6ada542f034c764964f0f61e1bf01"
        # model.grantType = 'authorization_code'
        # json_string = JsonUtil.to_json_string(model)
        # request.biz_model = json_string
        # 添加请求参数 方式二
        request.biz_model = {
            "companyId": 0
        }

        # 调用请求
        response = self.client.execute(request, user_token='5acae8efcb45c30e2afd24c18d55718c')
        # response = self.client.execute(request, app_token='aae26502e80b8885555ef7fb1162246a')

        if response.is_success():
            print('response: ', response)
            print('data: ', response.data)
        else:
            print("response: ", response)
            print('请求失败,request_id:%s, code:%s, msg:%s, sub_code:%s, sub_msg:%s' % \
                  (response.request_id, response.code, response.msg, response.sub_code, response.sub_msg))


if __name__ == '__main__':
    unittest.main()
