#!/usr/bin/python
# -*- coding: UTF-8 -*-
from common import RequestTypes
from request.BaseRequest import BaseRequest


class RpaPlansPostRequest(BaseRequest):
    """
    自用API-提交计划并创建任务
    通过输入计划参数，指定要运行的RPA、店铺、站点、任务运行周期等，创建对应的任务计划，计划提交后由系统自动创建对应的任务并执行。
    """

    def __init__(self):
        BaseRequest.__init__(self)

    def get_method(self):
        return '/rpa/company/plans'

    def get_request_type(self):
        return RequestTypes.POST_JSON
