#!/usr/bin/python
# -*- coding: UTF-8 -*-
from common import RequestTypes
from request.BaseRequest import BaseRequest


class RpaTasksGetRequest(BaseRequest):
    """
    自用API-查询任务执行记录
    通过时间范围来查询要执行和已执行的任务记录。一个计划会生成多个任务、一个任务下会有多个执行结果，每一个结果对应为一条数据输出。
    """

    def __init__(self):
        BaseRequest.__init__(self)

    def get_method(self):
        return '/rpa/company/tasks'

    def get_request_type(self):
        return RequestTypes.GET
