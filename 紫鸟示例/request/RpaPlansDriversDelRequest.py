#!/usr/bin/python
# -*- coding: UTF-8 -*-
from common import RequestTypes
from request.BaseRequest import BaseRequest


class RpaPlansDriversDelRequest(BaseRequest):
    """
    自用API-停用计划
    对于周期性计划，停用计划后，系统将不会在继续在创建对应的任务，该计划已经执行过的任务不变。停用后可继续启用。
    启用后，已经错过的任务不会重新执行。如周一创建了计划，计划每天24点执行一次RPA汇总，周一周二执行两次后，周三24点前停用该计划，周五24点前启用该计划至本周日结束，则该计划共运行一、二、五、六、七，五次。
    非周期性计划，没有停用，执行后立即结束。
    """

    def __init__(self):
        BaseRequest.__init__(self)

    def get_method(self):
        return '/rpa/company/plans/drivers'

    def get_request_type(self):
        return RequestTypes.DELETE
