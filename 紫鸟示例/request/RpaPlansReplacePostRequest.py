#!/usr/bin/python
# -*- coding: UTF-8 -*-
from common import RequestTypes
from request.BaseRequest import BaseRequest


class RpaPlansReplacePostRequest(BaseRequest):
    """
    自用API-修改计划并创建任务
    通过指定要修改的计划id，并重新传入计划参数，提交计划后，由系统按新计划自动生成任务，已经执行过的任务不会重新执行。
    """

    def __init__(self):
        BaseRequest.__init__(self)

    def get_method(self):
        return '/rpa/company/plans/replacement'

    def get_request_type(self):
        return RequestTypes.POST_JSON
