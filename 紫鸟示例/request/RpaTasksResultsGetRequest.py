#!/usr/bin/python
# -*- coding: UTF-8 -*-
from common import RequestTypes
from request.BaseRequest import BaseRequest


class RpaTasksResultsGetRequest(BaseRequest):
    """
    自用API-查询任务执行结果记录
    通过任务Id，查询该任务生成的任务记录，一个任务在多个站点、多个店铺、使用多个RPA执行时，每个站点、店铺、RPA都会生成一天任务执行结果。
    """

    def __init__(self):
        BaseRequest.__init__(self)

    def get_method(self):
        return '/rpa/company/tasks/results'

    def get_request_type(self):
        return RequestTypes.GET
