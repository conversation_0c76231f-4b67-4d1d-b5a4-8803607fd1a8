class Script:
    # 单个RPA的Id，在RPA市场中，点击查看RPA详情，可以看到RPA_ID字段
    scriptId = None

    # 参数列表 不同RPA可选的参数列表不一样，可通过获取RPA详情接口进行查看,对应接口返回中的runWith结果 如何填写RPA运行
    runWith = None

    @classmethod
    def init(cls):
        return cls(None, None)

    def __init__(self, scriptId, runWith):
        self.scriptId = scriptId
        self.runWith = runWith


class RunWith:
    """参数列表类"""
    # 参数名称
    name = None
    # 参数值
    value = None

    @classmethod
    def init(cls):
        return cls(None, None)

    def __init__(self, name, value):
        self.name = name
        self.value = value


class StoreAndMarketPlace:
    """店铺和站点类"""
    # 卖家标识 多个id用逗号间隔，指定是电商平台提供的卖家Id
    sellerIds = None
    # 站点 多个用逗号间隔，如：US、CA，参考第六节平台站点代码
    marketPlace = None

    @classmethod
    def init(cls):
        return cls(None, None)

    def __init__(self, sellerIds, marketPlace):
        self.sellerIds = sellerIds
        self.marketPlace = marketPlace
