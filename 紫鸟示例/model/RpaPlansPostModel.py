class RpaPlansPostModel:
    """自用API-提交计划并创建任务"""

    # 计划名称
    name = None
    # 平台id
    platformId = None
    # 执行类型
    """
    立即执行：AT_ONCE
    指定时间执行：ASSIGN_TIME
    按小时执行：REPEAT_BY_HOUR
    按天执行：REPEAT_BY_DAY
    按周执行：REPEAT_BY_WEEK
    按月执行：REPEAT_BY_MONTH
    """
    actionType = None
    # 指定运行时间    actionType为 ASSIGN_TIME：必填
    assignTime = None

    # 循环开始时间    actionType为REPEAT_BY_HOUR：选填 值为毫秒时间戳
    # 不填时默认为从当前时间5分钟后开始循环
    repeatFrom = None

    """
    按周期范围 
    actionType为
    REPEAT_BY_HOUR：必填
    REPEAT_BY_WEEK：必填
    REPEAT_BY_MONTH：必填
    按小时执行：REPEAT_BY_HOUR
    如：26 指每间隔26小时目前仅支持间隔26或48小时
    按月执行：REPEAT_BY_MONTH
    如：30 指每月30号取值范围1到31
    """
    repeatRange = None

    """
    按周期执行的时间点
    actionType为
    REPEAT_BY_DAY：必填
    REPEAT_BY_WEEK：必填
    REPEAT_BY_MONTH：必填
    格式 HH:mm 如：15:00 指对应日期的15点执行
    """
    repeatTimeAt = None

    # RPA列表
    scriptList = []

    # 店铺范围  ALL全部、ASSIGNED指定
    storeScopeType = None

    # 店铺和站点，storeScopeType值为ASSIGNED时必须指明要运行的店铺和站点
    storeAndMarketPlaceList = None

    # 计划有效期从
    validFrom = None
    # 计划有效期至
    validTo = None
    # 自定义扩展参数 JSON格式的map对象，该参数回透传给RPA执行器，可用于自行开发RPA或定制化RPA时传递自定义的复杂参数
    extInfo = None
