# Design Document

## Overview

本设计文档旨在解决店铺设置脚本中Selenium WebDriver的稳定性问题，特别是在切换国家时出现的间歇性崩溃。设计方案采用多层防护策略，包括智能重试机制、资源监控、错误恢复和可观测性增强。

## Architecture

### 核心架构原则

1. **防御性编程** - 假设任何操作都可能失败，提前做好错误处理
2. **渐进式降级** - 当某个功能失败时，系统能够优雅降级而不是完全崩溃
3. **可观测性优先** - 提供充分的日志和监控信息用于问题诊断
4. **资源管理** - 严格控制WebDriver实例的生命周期和资源使用

### 系统架构图

```mermaid
graph TB
    A[RPA脚本入口] --> B[稳定性管理器]
    B --> C[WebDriver工厂]
    B --> D[重试策略管理器]
    B --> E[资源监控器]
    B --> F[错误恢复器]
    
    C --> G[WebDriver实例池]
    D --> H[指数退避算法]
    D --> I[断路器模式]
    E --> J[内存监控]
    E --> K[性能指标收集]
    F --> L[页面状态检测]
    F --> M[元素查找策略]
    
    G --> N[Chrome浏览器实例]
    N --> O[目标网站]
```

## Components and Interfaces

### 1. WebDriverStabilityManager

**职责**: 作为稳定性增强的核心协调器

```python
class WebDriverStabilityManager:
    def __init__(self, config: StabilityConfig):
        self.config = config
        self.retry_manager = RetryManager(config.retry_config)
        self.resource_monitor = ResourceMonitor(config.monitor_config)
        self.error_recovery = ErrorRecovery(config.recovery_config)
        self.metrics_collector = MetricsCollector()
    
    def execute_with_stability(self, operation: Callable, context: OperationContext) -> OperationResult:
        """使用稳定性增强执行操作"""
        pass
    
    def handle_webdriver_crash(self, driver: WebDriver, error: Exception) -> WebDriver:
        """处理WebDriver崩溃并恢复"""
        pass
```

### 2. RetryManager

**职责**: 管理重试策略和断路器模式

```python
class RetryManager:
    def __init__(self, config: RetryConfig):
        self.max_retries = config.max_retries
        self.backoff_strategy = ExponentialBackoff(config.backoff_config)
        self.circuit_breaker = CircuitBreaker(config.circuit_config)
    
    def execute_with_retry(self, operation: Callable, context: RetryContext) -> Any:
        """使用重试机制执行操作"""
        pass
    
    def should_retry(self, error: Exception, attempt: int) -> bool:
        """判断是否应该重试"""
        pass
```

### 3. ResourceMonitor

**职责**: 监控系统资源使用情况

```python
class ResourceMonitor:
    def __init__(self, config: MonitorConfig):
        self.memory_threshold = config.memory_threshold
        self.cpu_threshold = config.cpu_threshold
        self.monitoring_interval = config.monitoring_interval
    
    def start_monitoring(self):
        """开始资源监控"""
        pass
    
    def check_resource_health(self) -> ResourceHealth:
        """检查资源健康状态"""
        pass
    
    def trigger_cleanup_if_needed(self) -> bool:
        """如果需要则触发清理"""
        pass
```

### 4. ErrorRecovery

**职责**: 处理各种错误情况并尝试恢复

```python
class ErrorRecovery:
    def __init__(self, config: RecoveryConfig):
        self.element_strategies = ElementFindingStrategies()
        self.page_state_detector = PageStateDetector()
        self.webdriver_restarter = WebDriverRestarter()
    
    def recover_from_element_not_found(self, locator: Locator, driver: WebDriver) -> WebElement:
        """从元素未找到错误中恢复"""
        pass
    
    def recover_from_page_load_timeout(self, driver: WebDriver, url: str) -> bool:
        """从页面加载超时中恢复"""
        pass
    
    def recover_from_webdriver_crash(self, driver: WebDriver) -> WebDriver:
        """从WebDriver崩溃中恢复"""
        pass
```

### 5. EnhancedWebDriverFactory

**职责**: 创建和管理增强的WebDriver实例

```python
class EnhancedWebDriverFactory:
    def __init__(self, config: WebDriverConfig):
        self.config = config
        self.instance_pool = WebDriverPool(config.pool_config)
        self.health_checker = WebDriverHealthChecker()
    
    def create_driver(self, capabilities: Dict) -> EnhancedWebDriver:
        """创建增强的WebDriver实例"""
        pass
    
    def get_healthy_driver(self) -> EnhancedWebDriver:
        """获取健康的WebDriver实例"""
        pass
    
    def cleanup_unhealthy_drivers(self):
        """清理不健康的WebDriver实例"""
        pass
```

## Data Models

### 配置模型

```python
@dataclass
class StabilityConfig:
    retry_config: RetryConfig
    monitor_config: MonitorConfig
    recovery_config: RecoveryConfig
    webdriver_config: WebDriverConfig

@dataclass
class RetryConfig:
    max_retries: int = 3
    initial_delay: float = 1.0
    max_delay: float = 30.0
    exponential_base: float = 2.0
    jitter: bool = True
    
@dataclass
class MonitorConfig:
    memory_threshold_mb: int = 2048
    cpu_threshold_percent: int = 80
    monitoring_interval_seconds: int = 30
    enable_performance_logging: bool = True

@dataclass
class RecoveryConfig:
    element_find_timeout: int = 30
    page_load_timeout: int = 60
    max_recovery_attempts: int = 2
    screenshot_on_error: bool = True
```

### 运行时模型

```python
@dataclass
class OperationContext:
    operation_name: str
    country: str
    store_info: Dict
    attempt_number: int
    start_time: datetime
    metadata: Dict[str, Any]

@dataclass
class OperationResult:
    success: bool
    result: Any
    error: Optional[Exception]
    execution_time: float
    retry_count: int
    recovery_actions: List[str]

@dataclass
class ResourceHealth:
    memory_usage_mb: int
    cpu_usage_percent: float
    is_healthy: bool
    warnings: List[str]
    recommendations: List[str]
```

## Error Handling

### 错误分类和处理策略

#### 1. 可重试错误 (Retryable Errors)
- **TimeoutException**: 页面加载或元素查找超时
- **NoSuchElementException**: 元素未找到（可能是页面未完全加载）
- **WebDriverException**: 通用WebDriver异常
- **ConnectionError**: 网络连接问题

**处理策略**: 使用指数退避重试，最多3次

#### 2. 不可重试错误 (Non-Retryable Errors)
- **InvalidSelectorException**: 选择器语法错误
- **InvalidArgumentException**: 参数错误
- **SessionNotCreatedException**: 会话创建失败

**处理策略**: 立即失败，记录详细错误信息

#### 3. 系统级错误 (System-Level Errors)
- **Chrome进程崩溃**: 内存不足或系统资源问题
- **WebDriver连接丢失**: 进程意外终止

**处理策略**: 重启WebDriver实例，清理资源

### 错误恢复流程

```mermaid
flowchart TD
    A[操作执行] --> B{是否成功?}
    B -->|是| C[返回结果]
    B -->|否| D[错误分类]
    
    D --> E{可重试?}
    E -->|是| F[检查重试次数]
    E -->|否| G[记录错误并失败]
    
    F --> H{未达到上限?}
    H -->|是| I[应用退避策略]
    H -->|否| J[尝试错误恢复]
    
    I --> K[等待]
    K --> A
    
    J --> L{恢复成功?}
    L -->|是| A
    L -->|否| G
```

## Testing Strategy

### 1. 单元测试

**测试范围**:
- 重试机制的正确性
- 错误分类逻辑
- 资源监控阈值检测
- 配置验证

**测试工具**: pytest, unittest.mock

### 2. 集成测试

**测试场景**:
- WebDriver崩溃恢复
- 网络中断处理
- 内存不足情况
- 并发操作稳定性

**测试环境**: Docker容器 + Selenium Grid

### 3. 压力测试

**测试目标**:
- 长时间运行稳定性
- 高并发场景下的表现
- 资源泄漏检测
- 性能基准测试

**测试工具**: pytest-benchmark, memory_profiler

### 4. 故障注入测试

**故障类型**:
- 随机网络延迟
- 内存限制
- CPU限制
- 进程意外终止

**验证指标**:
- 错误恢复成功率 ≥ 90%
- 平均恢复时间 ≤ 30秒
- 资源泄漏率 = 0%

## Implementation Details

### 智能元素查找策略

```python
class ElementFindingStrategies:
    def __init__(self):
        self.strategies = [
            self.find_by_xpath,
            self.find_by_css_selector,
            self.find_by_id,
            self.find_by_class_name,
            self.find_by_text_content
        ]
    
    def find_element_with_fallback(self, driver: WebDriver, locator: Locator) -> WebElement:
        """使用多种策略查找元素"""
        for strategy in self.strategies:
            try:
                element = strategy(driver, locator)
                if element and element.is_displayed():
                    return element
            except Exception as e:
                log_debug(f"Strategy {strategy.__name__} failed: {e}")
                continue
        
        raise NoSuchElementException(f"Element not found with any strategy: {locator}")
```

### 页面状态检测

```python
class PageStateDetector:
    def wait_for_page_ready(self, driver: WebDriver, timeout: int = 30) -> bool:
        """等待页面完全加载"""
        try:
            WebDriverWait(driver, timeout).until(
                lambda d: d.execute_script("return document.readyState") == "complete"
            )
            
            # 等待jQuery完成（如果存在）
            WebDriverWait(driver, 5).until(
                lambda d: d.execute_script("return typeof jQuery === 'undefined' || jQuery.active === 0")
            )
            
            # 等待Angular完成（如果存在）
            WebDriverWait(driver, 5).until(
                lambda d: d.execute_script(
                    "return typeof angular === 'undefined' || "
                    "angular.element(document).injector().get('$http').pendingRequests.length === 0"
                )
            )
            
            return True
        except TimeoutException:
            return False
```

### 资源监控实现

```python
class ResourceMonitor:
    def __init__(self, config: MonitorConfig):
        self.config = config
        self.monitoring_thread = None
        self.is_monitoring = False
        self.metrics = defaultdict(list)
    
    def start_monitoring(self):
        """启动资源监控线程"""
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        self.monitoring_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitoring_thread.start()
    
    def _monitor_loop(self):
        """监控循环"""
        while self.is_monitoring:
            try:
                health = self._collect_metrics()
                self._check_thresholds(health)
                time.sleep(self.config.monitoring_interval_seconds)
            except Exception as e:
                log_error(f"Resource monitoring error: {e}")
    
    def _collect_metrics(self) -> ResourceHealth:
        """收集系统指标"""
        import psutil
        
        memory_usage = psutil.virtual_memory().used // (1024 * 1024)  # MB
        cpu_usage = psutil.cpu_percent(interval=1)
        
        # 检查Chrome进程
        chrome_processes = [p for p in psutil.process_iter(['pid', 'name', 'memory_info']) 
                          if 'chrome' in p.info['name'].lower()]
        
        total_chrome_memory = sum(p.info['memory_info'].rss for p in chrome_processes) // (1024 * 1024)
        
        is_healthy = (
            memory_usage < self.config.memory_threshold_mb and
            cpu_usage < self.config.cpu_threshold_percent
        )
        
        warnings = []
        if memory_usage > self.config.memory_threshold_mb * 0.8:
            warnings.append(f"Memory usage high: {memory_usage}MB")
        if cpu_usage > self.config.cpu_threshold_percent * 0.8:
            warnings.append(f"CPU usage high: {cpu_usage}%")
        if total_chrome_memory > 1024:  # 1GB
            warnings.append(f"Chrome memory usage high: {total_chrome_memory}MB")
        
        return ResourceHealth(
            memory_usage_mb=memory_usage,
            cpu_usage_percent=cpu_usage,
            is_healthy=is_healthy,
            warnings=warnings,
            recommendations=self._generate_recommendations(memory_usage, cpu_usage)
        )
```

## Performance Considerations

### 1. 内存管理
- WebDriver实例池化，避免频繁创建销毁
- 定期清理不活跃的浏览器实例
- 监控内存使用，超过阈值时主动清理

### 2. 并发控制
- 限制同时运行的WebDriver实例数量
- 使用信号量控制资源访问
- 避免竞态条件

### 3. 网络优化
- 实现智能重试，避免网络抖动影响
- 使用连接池复用HTTP连接
- 设置合理的超时时间

### 4. 缓存策略
- 缓存页面元素定位信息
- 缓存页面加载状态
- 避免重复的DOM查询

## Security Considerations

### 1. 敏感信息保护
- 配置文件中的密码加密存储
- 日志中屏蔽敏感信息
- 截图时避免包含敏感数据

### 2. 资源访问控制
- 限制WebDriver可访问的文件系统路径
- 控制网络访问权限
- 防止恶意脚本注入

### 3. 进程隔离
- 每个WebDriver实例运行在独立进程中
- 限制进程权限
- 监控异常进程行为