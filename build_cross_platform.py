#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
ShopSetup 跨平台打包脚本
支持 Windows 和 macOS 平台
"""

import os
import sys
import shutil
import subprocess
import platform
from pathlib import Path
import json

class CrossPlatformBuilder:
    def __init__(self):
        self.platform = platform.system().lower()
        self.is_windows = self.platform == 'windows'
        self.is_macos = self.platform == 'darwin'
        self.is_linux = self.platform == 'linux'
        
        # 平台特定配置
        self.config = self._get_platform_config()
        
    def _get_platform_config(self):
        """获取平台特定配置"""
        base_config = {
            'app_name': 'ShopSetup',
            'main_script': 'script/shopSetup.py',
            'icon_file': None,
            'console': False,
            'onefile': True
        }
        
        if self.is_windows:
            base_config.update({
                'exe_name': 'ShopSetup.exe',
                'dist_name': 'ShopSetup_Windows',
                'icon_file': 'assets/icon.ico' if os.path.exists('assets/icon.ico') else None,
                'additional_args': ['--uac-admin']  # Windows管理员权限
            })
        elif self.is_macos:
            base_config.update({
                'exe_name': 'ShopSetup',
                'dist_name': 'ShopSetup_macOS',
                'icon_file': 'assets/icon.icns' if os.path.exists('assets/icon.icns') else None,
                'additional_args': ['--osx-bundle-identifier=com.ziniao.shopsetup']
            })
        elif self.is_linux:
            base_config.update({
                'exe_name': 'ShopSetup',
                'dist_name': 'ShopSetup_Linux',
                'icon_file': 'assets/icon.png' if os.path.exists('assets/icon.png') else None,
                'additional_args': []
            })
        
        return base_config
    
    def clean_build_dirs(self):
        """清理构建目录"""
        print("🧹 清理构建目录...")
        
        dirs_to_clean = ['build', 'dist', '__pycache__']
        for dir_name in dirs_to_clean:
            if os.path.exists(dir_name):
                shutil.rmtree(dir_name)
                print(f"   已删除 {dir_name}")
        
        # 删除 .spec 文件
        spec_files = [f for f in os.listdir('.') if f.endswith('.spec')]
        for spec_file in spec_files:
            os.remove(spec_file)
            print(f"   已删除 {spec_file}")
    
    def check_dependencies(self):
        """检查依赖"""
        print("🔍 检查依赖...")
        
        # 检查Python版本
        python_version = sys.version_info
        print(f"   Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
        
        if python_version < (3, 8):
            print("❌ 错误: 需要Python 3.8或更高版本")
            return False
        
        # 检查PyInstaller
        try:
            import PyInstaller
            print(f"   PyInstaller版本: {PyInstaller.__version__}")
        except ImportError:
            print("❌ 错误: 未安装PyInstaller，请运行: pip install PyInstaller")
            return False
        
        # 检查必要文件
        required_files = [
            'script/shopSetup.py',
            'ziniao_rpa_base.py',
            'gui_framework.py',
            'gui_utils.py',
            'global_logger.py'
        ]
        
        for file_path in required_files:
            if not os.path.exists(file_path):
                print(f"❌ 错误: 找不到必要文件 - {file_path}")
                return False
            print(f"   ✅ {file_path}")
        
        return True
    
    def build_executable(self):
        """构建可执行文件"""
        print(f"🔨 开始构建 {self.platform.title()} 版本...")
        
        # 构建PyInstaller命令
        cmd = [
            'pyinstaller',
            '--clean',
            '--noconfirm',
            f'--name={self.config["app_name"]}',
            '--distpath=dist',
            '--workpath=build'
        ]
        
        # 添加平台特定参数
        if self.config['onefile']:
            cmd.append('--onefile')
        
        if not self.config['console']:
            if self.is_windows:
                cmd.append('--noconsole')
            elif self.is_macos:
                cmd.append('--windowed')
        
        # 添加图标
        if self.config['icon_file'] and os.path.exists(self.config['icon_file']):
            cmd.append(f'--icon={self.config["icon_file"]}')
            print(f"   使用图标: {self.config['icon_file']}")
        
        # 添加数据文件
        data_files = [
            'ziniao_config.json',
            'gui_framework.py',
            'gui_utils.py', 
            'ziniao_rpa_base.py',
            'ziniao_socket_client.py',
            'global_logger.py'
        ]
        
        for data_file in data_files:
            if os.path.exists(data_file):
                if self.is_windows:
                    cmd.append(f'--add-data={data_file};.')
                else:
                    cmd.append(f'--add-data={data_file}:.')
        
        # 添加隐藏导入
        hidden_imports = [
            'selenium', 'selenium.webdriver', 'selenium.webdriver.chrome',
            'selenium.webdriver.common.by', 'selenium.webdriver.support.ui',
            'selenium.webdriver.support.expected_conditions',
            'selenium.webdriver.common.action_chains', 'selenium.webdriver.common.keys',
            'selenium.webdriver.chrome.service', 'selenium.webdriver.chrome.options',
            'selenium.common.exceptions', 'pyautogui', 'pyperclip',
            'requests', 'pandas', 'openpyxl', 'tkinter', 'tkinter.ttk',
            'tkinter.messagebox', 'tkinter.filedialog', 'tkinter.scrolledtext',
            'json', 'threading', 'time', 'os', 'sys', 'traceback', 'uuid', 'socket'
        ]

        # 添加Windows特定的隐藏导入
        if self.is_windows:
            windows_imports = [
                'win32gui', 'win32con', 'win32api', 'win32process', 'win32clipboard',
                'pywintypes', 'pythoncom', 'win32com', 'win32com.client'
            ]
            hidden_imports.extend(windows_imports)
        
        for import_name in hidden_imports:
            cmd.append(f'--hidden-import={import_name}')
        
        # 添加平台特定参数
        if 'additional_args' in self.config:
            cmd.extend(self.config['additional_args'])
        
        # 添加主脚本
        cmd.append(self.config['main_script'])
        
        print(f"   执行命令: {' '.join(cmd[:5])}...")
        
        # 执行构建
        try:
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            print("   ✅ 构建成功!")
            return True
        except subprocess.CalledProcessError as e:
            print(f"   ❌ 构建失败: {e}")
            print(f"   错误输出: {e.stderr}")
            return False
    
    def create_distribution_package(self):
        """创建分发包"""
        print(f"📦 创建 {self.platform.title()} 分发包...")
        
        # 检查生成的可执行文件
        if self.is_windows:
            exe_path = f'dist/{self.config["app_name"]}.exe'
        else:
            exe_path = f'dist/{self.config["app_name"]}'
        
        if not os.path.exists(exe_path):
            print(f"❌ 错误: 找不到生成的可执行文件 - {exe_path}")
            return False
        
        # 创建分发目录
        dist_dir = Path(self.config['dist_name'])
        if dist_dir.exists():
            shutil.rmtree(dist_dir)
        dist_dir.mkdir()
        
        # 复制可执行文件
        if self.is_windows:
            shutil.copy2(exe_path, dist_dir / '店铺设置.exe')
        else:
            shutil.copy2(exe_path, dist_dir / '店铺设置')
            # 确保可执行权限
            os.chmod(dist_dir / '店铺设置', 0o755)
        
        # 复制配置文件
        config_files = ['ziniao_config.json']
        for config_file in config_files:
            if os.path.exists(config_file):
                shutil.copy2(config_file, dist_dir)
        
        # 创建使用说明
        self._create_readme(dist_dir)
        
        # 创建启动脚本（macOS和Linux）
        if not self.is_windows:
            self._create_launch_script(dist_dir)
        
        print(f"   ✅ 分发包已创建: {dist_dir}")
        return True
    
    def _create_readme(self, dist_dir):
        """创建使用说明"""
        platform_name = "Windows" if self.is_windows else "macOS" if self.is_macos else "Linux"
        
        readme_content = f"""店铺设置工具 - {platform_name} 版本
================================

1. 系统要求：
   - {platform_name} 操作系统
   - Chrome 浏览器
   - 稳定的网络连接

2. 使用方法：
"""
        
        if self.is_windows:
            readme_content += """   - 双击 "店铺设置.exe" 启动程序
   - 如遇到安全提示，请选择"仍要运行"
"""
        else:
            readme_content += """   - 双击 "启动店铺设置.command" 启动程序
   - 或在终端中运行: ./店铺设置
   - 首次运行可能需要授权
"""
        
        readme_content += """
3. 配置文件：
   - ziniao_config.json：主要配置文件
   - 程序会自动创建和管理配置

4. 注意事项：
   - 首次运行可能需要较长启动时间
   - 请确保网络连接正常
   - 建议关闭防火墙和杀毒软件的拦截

5. 故障排除：
   - 如果程序无法启动，请检查系统权限
   - 确保Chrome浏览器已正确安装
   - 查看日志文件了解详细错误信息

更新日期: 2024-12-01
版本: 2.0
"""
        
        with open(dist_dir / 'README.txt', 'w', encoding='utf-8') as f:
            f.write(readme_content)
    
    def _create_launch_script(self, dist_dir):
        """创建启动脚本（macOS/Linux）"""
        if self.is_macos:
            script_content = '''#!/bin/bash
cd "$(dirname "$0")"
./店铺设置
'''
            script_path = dist_dir / '启动店铺设置.command'
        else:
            script_content = '''#!/bin/bash
cd "$(dirname "$0")"
./店铺设置
'''
            script_path = dist_dir / '启动店铺设置.sh'
        
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        # 设置可执行权限
        os.chmod(script_path, 0o755)
    
    def build(self):
        """执行完整构建流程"""
        print(f"🚀 开始构建 {self.platform.title()} 版本的店铺设置工具")
        print(f"   平台: {platform.platform()}")
        print(f"   架构: {platform.machine()}")
        print()
        
        try:
            # 步骤1: 检查依赖
            if not self.check_dependencies():
                return False
            
            # 步骤2: 清理构建目录
            self.clean_build_dirs()
            
            # 步骤3: 构建可执行文件
            if not self.build_executable():
                return False
            
            # 步骤4: 创建分发包
            if not self.create_distribution_package():
                return False
            
            print()
            print("🎉 构建完成!")
            print(f"   生成的文件: {self.config['dist_name']}/")
            print(f"   平台: {self.platform.title()}")
            print()
            
            return True
            
        except Exception as e:
            print(f"❌ 构建过程中发生错误: {e}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """主函数"""
    print("=" * 50)
    print("    店铺设置工具 - 跨平台打包脚本")
    print("=" * 50)
    print()
    
    builder = CrossPlatformBuilder()
    
    if not (builder.is_windows or builder.is_macos or builder.is_linux):
        print(f"❌ 不支持的平台: {platform.system()}")
        return False
    
    success = builder.build()
    
    if success:
        print("✅ 打包成功完成!")
        print()
        print("📁 生成的文件:")
        print(f"   - {builder.config['dist_name']}/ (推荐使用)")
        print()
        print("💡 提示:")
        print("   - 可以将整个文件夹复制到其他电脑使用")
        print("   - 首次运行可能需要系统授权")
        print("   - 详细说明请查看 README.txt")
    else:
        print("❌ 打包失败!")
        print("   请检查错误信息并重试")
    
    return success

if __name__ == "__main__":
    success = main()
    
    if not success:
        sys.exit(1)
