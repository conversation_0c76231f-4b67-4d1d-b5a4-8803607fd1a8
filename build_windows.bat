@echo off
chcp 65001 >nul
title 店铺设置工具 - Windows 打包

echo ========================================
echo      店铺设置工具 - Windows 打包
echo ========================================
echo.

echo 正在检查Python环境...
python --version
if %errorlevel% neq 0 (
    echo 错误: 未找到Python环境，请先安装Python 3.8+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo.
echo 正在检查PyInstaller...
python -c "import PyInstaller; print('PyInstaller版本:', PyInstaller.__version__)"
if %errorlevel% neq 0 (
    echo 正在安装PyInstaller...
    pip install PyInstaller
    if %errorlevel% neq 0 (
        echo 错误: PyInstaller安装失败
        pause
        exit /b 1
    )
)

echo.
echo 正在检查必要文件...
if not exist "script\shopSetup.py" (
    echo 错误: 找不到 script\shopSetup.py 文件
    pause
    exit /b 1
)

if not exist "ziniao_rpa_base.py" (
    echo 错误: 找不到 ziniao_rpa_base.py 文件
    pause
    exit /b 1
)

echo.
echo 开始Windows版本打包...
python build_cross_platform.py

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo           打包成功完成！
    echo ========================================
    echo.
    echo 生成的文件：
    echo - ShopSetup_Windows\ (Windows版本)
    echo.
    echo 您可以将 ShopSetup_Windows 文件夹复制到其他Windows电脑使用
    echo.
) else (
    echo.
    echo ========================================
    echo           打包失败！
    echo ========================================
    echo.
    echo 请检查上面的错误信息并重试
    echo.
)

pause
