"""
全局日志模块

提供全局可用的日志功能，支持输出到GUI界面和控制台
在script包中的脚本可以直接使用这些日志函数
"""

import logging
import threading
import time
from datetime import datetime
from typing import Optional, Any
import tkinter as tk


class GlobalLogHandler(logging.Handler):
    """全局日志处理器，支持输出到GUI和控制台"""
    
    def __init__(self):
        super().__init__()
        self.log_widget = None
        self.formatter = logging.Formatter(
            '%(asctime)s [%(levelname)s] %(message)s',
            datefmt='%H:%M:%S'
        )
        self._lock = threading.Lock()
    
    def set_log_widget(self, log_widget):
        """设置日志显示组件"""
        with self._lock:
            self.log_widget = log_widget
    
    def emit(self, record):
        """输出日志记录"""
        try:
            # 格式化日志消息
            msg = self.format(record)
            
            # 输出到控制台
            print(msg)
            
            # 输出到GUI（如果可用）
            if self.log_widget is not None:
                self._update_gui_log(msg)
                
        except Exception:
            pass  # 忽略日志处理错误，避免影响程序运行
    
    def _update_gui_log(self, msg):
        """更新GUI日志显示"""
        def update_log():
            try:
                self.log_widget.insert(tk.END, msg + '\n')
                # 自动滚动到底部
                self.log_widget.see(tk.END)
                # 限制日志行数，避免内存占用过大
                lines = self.log_widget.get(1.0, tk.END).count('\n')
                if lines > 1000:  # 超过1000行删除前面的
                    self.log_widget.delete(1.0, f"{lines-800}.0")
            except:
                pass  # 忽略GUI更新错误
        
        # 如果在主线程中则直接更新，否则安排到主线程
        try:
            if threading.current_thread() == threading.main_thread():
                update_log()
            else:
                self.log_widget.after_idle(update_log)
        except:
            pass


class GlobalLogger:
    """全局日志管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger('GlobalLogger')
        self.logger.setLevel(logging.DEBUG)
        
        # 创建全局日志处理器
        self.handler = GlobalLogHandler()
        self.logger.addHandler(self.handler)
        
        # 避免重复日志
        self.logger.propagate = False
    
    def set_log_widget(self, log_widget):
        """设置GUI日志组件"""
        self.handler.set_log_widget(log_widget)
    
    def log(self, message: str, level: int = logging.INFO):
        """输出日志"""
        self.logger.log(level, message)
    
    def info(self, message: str):
        """输出信息日志"""
        self.logger.info(message)
    
    def warning(self, message: str):
        """输出警告日志"""
        self.logger.warning(message)
    
    def error(self, message: str):
        """输出错误日志"""
        self.logger.error(message)
    
    def debug(self, message: str):
        """输出调试日志"""
        self.logger.debug(message)
    
    def success(self, message: str):
        """输出成功日志（使用INFO级别）"""
        self.logger.info(f"✅ {message}")
    
    def step(self, message: str):
        """输出步骤日志（使用INFO级别）"""
        self.logger.info(f"🔄 {message}")
    
    def task_start(self, task_name: str):
        """任务开始日志"""
        self.logger.info(f"🚀 开始执行任务: {task_name}")
    
    def task_end(self, task_name: str, success: bool = True):
        """任务结束日志"""
        if success:
            self.logger.info(f"✅ 任务完成: {task_name}")
        else:
            self.logger.error(f"❌ 任务失败: {task_name}")
    
    def separator(self, title: str = ""):
        """输出分隔线"""
        if title:
            self.logger.info(f"{'='*20} {title} {'='*20}")
        else:
            self.logger.info("="*50)


# 创建全局日志实例
_global_logger = GlobalLogger()

# 导出全局日志函数，供其他模块直接使用
def set_log_widget(log_widget):
    """设置GUI日志组件"""
    _global_logger.set_log_widget(log_widget)

def log(message: str, level: int = logging.INFO):
    """输出日志"""
    _global_logger.log(message, level)

def log_info(message: str):
    """输出信息日志"""
    _global_logger.info(message)

def log_warning(message: str):
    """输出警告日志"""
    _global_logger.warning(message)

def log_error(message: str):
    """输出错误日志"""
    _global_logger.error(message)

def log_debug(message: str):
    """输出调试日志"""
    _global_logger.debug(message)

def log_success(message: str):
    """输出成功日志"""
    _global_logger.success(message)

def log_step(message: str):
    """输出步骤日志"""
    _global_logger.step(message)

def log_task_start(task_name: str):
    """任务开始日志"""
    _global_logger.task_start(task_name)

def log_task_end(task_name: str, success: bool = True):
    """任务结束日志"""
    _global_logger.task_end(task_name, success)

def log_separator(title: str = ""):
    """输出分隔线"""
    _global_logger.separator(title)

# 获取全局日志实例（供需要更高级功能的模块使用）
def get_global_logger() -> GlobalLogger:
    """获取全局日志实例"""
    return _global_logger 