#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
ShopSetup 打包配置脚本
用于将 ShopSetup 项目打包成 exe 文件
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def clean_build_dirs():
    """清理之前的构建目录"""
    print("正在清理之前的构建目录...")
    
    dirs_to_clean = ['build', 'dist', '__pycache__']
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"已删除 {dir_name} 目录")
    
    # 删除 .spec 文件
    spec_files = [f for f in os.listdir('.') if f.endswith('.spec')]
    for spec_file in spec_files:
        os.remove(spec_file)
        print(f"已删除 {spec_file}")

def create_build_dir():
    """创建构建目录结构"""
    print("正在创建构建目录...")
    
    build_dir = Path('build_exe')
    if build_dir.exists():
        shutil.rmtree(build_dir)
    
    build_dir.mkdir()
    
    # 创建配置文件目录
    config_dir = build_dir / 'config'
    config_dir.mkdir()
    
    # 复制配置文件
    if os.path.exists('ziniao_config.json'):
        shutil.copy2('ziniao_config.json', config_dir)
        print("已复制配置文件")
    
    return build_dir

def build_exe():
    """构建exe文件"""
    print("正在构建exe文件...")
    
    # PyInstaller命令参数
    cmd = [
        'pyinstaller',
        '--onefile',  # 打包成单个exe文件
        '--noconsole',  # 无控制台窗口（GUI应用）
        '--name=ShopSetup',  # exe文件名
        '--clean',  # 清理临时文件
        '--noconfirm',  # 不询问覆盖
        '--distpath=dist',  # 输出目录
        '--workpath=build',  # 临时目录
        
        # 添加数据文件
        '--add-data=ziniao_config.json;.',
        '--add-data=gui_framework.py;.',
        '--add-data=gui_utils.py;.',
        '--add-data=ziniao_rpa_base.py;.',
        '--add-data=global_logger.py;.',
        '--add-data=rpa_template.py;.',
        
        # 隐藏导入（确保所有依赖都被包含）
        '--hidden-import=selenium',
        '--hidden-import=selenium.webdriver',
        '--hidden-import=selenium.webdriver.chrome',
        '--hidden-import=selenium.webdriver.common.by',
        '--hidden-import=selenium.webdriver.support.ui',
        '--hidden-import=selenium.webdriver.support.expected_conditions',
        '--hidden-import=selenium.webdriver.common.action_chains',
        '--hidden-import=selenium.webdriver.common.keys',
        '--hidden-import=selenium.webdriver.chrome.service',
        '--hidden-import=selenium.webdriver.chrome.options',
        '--hidden-import=selenium.common.exceptions',
        '--hidden-import=selenium.common',
        '--hidden-import=pyautogui',
        '--hidden-import=pyperclip',
        '--hidden-import=requests',
        '--hidden-import=pandas',
        '--hidden-import=openpyxl',
        '--hidden-import=loguru',
        '--hidden-import=yaml',
        '--hidden-import=PIL',
        '--hidden-import=tkinter',
        '--hidden-import=tkinter.ttk',
        '--hidden-import=tkinter.messagebox',
        '--hidden-import=tkinter.filedialog',
        '--hidden-import=traceback',
        
        # 主入口文件
        'script/shopSetup.py'
    ]
    
    # 执行打包命令
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("打包成功!")
        print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"打包失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def create_portable_package():
    """创建便携式包"""
    print("正在创建便携式包...")
    
    if not os.path.exists('dist/ShopSetup.exe'):
        print("错误: 找不到生成的exe文件")
        return False
    
    # 创建便携式目录
    portable_dir = Path('ShopSetup_Portable')
    if portable_dir.exists():
        shutil.rmtree(portable_dir)
    portable_dir.mkdir()
    
    # 复制exe文件
    shutil.copy2('dist/ShopSetup.exe', portable_dir)
    
    # 复制配置文件
    if os.path.exists('ziniao_config.json'):
        shutil.copy2('ziniao_config.json', portable_dir)
    
    # 创建使用说明
    readme_content = """ShopSetup 便携版使用说明
================================

1. 运行要求：
   - Windows 10/11 操作系统
   - 需要安装 Chrome 浏览器
   - 需要下载对应版本的 ChromeDriver（程序会自动检测）

2. 使用方法：
   - 双击 ShopSetup.exe 启动程序
   - 按照界面提示操作

3. 配置文件：
   - ziniao_config.json：主要配置文件
   - 如需修改配置，请编辑此文件

4. 注意事项：
   - 首次运行可能需要较长启动时间
   - 请确保网络连接正常
   - 建议关闭杀毒软件的实时保护以避免误报

5. 问题反馈：
   - 如遇到问题，请保存日志文件并联系技术支持

更新日期: 2024-12-01
"""
    
    with open(portable_dir / 'README.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"便携式包已创建: {portable_dir}")
    return True

def main():
    """主函数"""
    print("=== ShopSetup 打包工具 ===")
    print("开始打包流程...")
    
    # 检查Python环境
    print(f"Python版本: {sys.version}")
    print(f"当前工作目录: {os.getcwd()}")
    
    # 检查必要文件
    required_files = [
        'script/shopSetup.py',
        'ziniao_rpa_base.py',
        'gui_framework.py',
        'requirements.txt'
    ]
    
    for file_path in required_files:
        if not os.path.exists(file_path):
            print(f"错误: 找不到必要文件 - {file_path}")
            return False
    
    try:
        # 步骤1: 清理之前的构建
        clean_build_dirs()
        
        # 步骤2: 构建exe
        if not build_exe():
            print("构建失败，请检查错误信息")
            return False
        
        # 步骤3: 创建便携式包
        if not create_portable_package():
            print("创建便携式包失败")
            return False
        
        print("\n=== 打包完成 ===")
        print("生成的文件:")
        print("- dist/ShopSetup.exe (单个exe文件)")
        print("- ShopSetup_Portable/ (便携式包，推荐使用)")
        print("\n您可以将 ShopSetup_Portable 文件夹复制到其他电脑上使用")
        
        return True
        
    except Exception as e:
        print(f"打包过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n按任意键退出...")
        input()
    else:
        print("\n打包失败，按任意键退出...")
        input()
        sys.exit(1) 