# 店铺设置工具 - 跨平台打包指南

## 📋 概述

本指南介绍如何将店铺设置工具打包成Windows和macOS的可执行文件。

## 🛠️ 环境要求

### 通用要求
- Python 3.8 或更高版本
- pip 包管理器
- 稳定的网络连接

### Windows 要求
- Windows 10/11
- PowerShell 或命令提示符

### macOS 要求
- macOS 10.14 或更高版本
- Xcode Command Line Tools
- Terminal

## 📦 打包方法

### 方法一：自动打包（推荐）

#### Windows 打包
```bash
# 双击运行或在命令行执行
build_windows.bat
```

#### macOS 打包
```bash
# 在终端中执行
chmod +x build_macos.sh
./build_macos.sh
```

### 方法二：手动打包

#### 1. 安装依赖
```bash
# 安装PyInstaller
pip install PyInstaller

# 安装项目依赖
pip install -r requirements.txt
```

#### 2. 执行跨平台构建脚本
```bash
python build_cross_platform.py
```

## 📁 输出文件

### Windows 版本
```
ShopSetup_Windows/
├── 店铺设置.exe          # 主程序
├── ziniao_config.json    # 配置文件
└── README.txt           # 使用说明
```

### macOS 版本
```
ShopSetup_macOS/
├── 店铺设置              # 主程序
├── 启动店铺设置.command   # 启动脚本
├── ziniao_config.json    # 配置文件
└── README.txt           # 使用说明
```

## 🔧 高级配置

### 自定义图标
将图标文件放置在 `assets/` 目录下：
- Windows: `assets/icon.ico`
- macOS: `assets/icon.icns`
- Linux: `assets/icon.png`

### 修改应用信息
编辑 `build_cross_platform.py` 中的配置：
```python
base_config = {
    'app_name': 'ShopSetup',           # 应用名称
    'main_script': 'script/shopSetup.py',  # 主脚本
    'console': False,                  # 是否显示控制台
    'onefile': True                    # 是否打包成单文件
}
```

## 🚀 分发指南

### Windows 分发
1. 将 `ShopSetup_Windows` 文件夹压缩成 ZIP
2. 用户解压后双击 `店铺设置.exe` 运行
3. 首次运行可能触发Windows Defender警告，选择"仍要运行"

### macOS 分发
1. 将 `ShopSetup_macOS` 文件夹压缩成 ZIP
2. 用户解压后双击 `启动店铺设置.command` 运行
3. 首次运行需要在"系统偏好设置 > 安全性与隐私"中允许运行

## 🐛 故障排除

### 常见问题

#### 1. PyInstaller 安装失败
```bash
# 升级pip
python -m pip install --upgrade pip

# 重新安装PyInstaller
pip install --force-reinstall PyInstaller
```

#### 2. 缺少依赖模块
```bash
# 安装完整依赖
pip install -r requirements.txt

# 手动安装缺少的模块
pip install selenium pyautogui requests pandas
```

#### 3. 打包后程序无法启动
- 检查是否缺少数据文件
- 查看控制台输出的错误信息
- 确保所有依赖都已正确打包

#### 4. macOS 权限问题
```bash
# 给予执行权限
chmod +x build_macos.sh
chmod +x ShopSetup_macOS/店铺设置
chmod +x ShopSetup_macOS/启动店铺设置.command
```

### 调试模式
如需调试，可以修改配置启用控制台：
```python
'console': True  # 显示控制台窗口
```

## 📝 版本管理

### 更新版本号
1. 修改 `build_cross_platform.py` 中的版本信息
2. 更新 README.txt 中的版本号和日期
3. 重新打包

### 发布清单
- [ ] 测试所有功能正常
- [ ] 更新版本号
- [ ] 生成Windows版本
- [ ] 生成macOS版本
- [ ] 测试打包后的程序
- [ ] 准备发布说明

## 🔗 相关文件

- `build_cross_platform.py` - 跨平台构建脚本
- `build_windows.bat` - Windows自动构建脚本
- `build_macos.sh` - macOS自动构建脚本
- `requirements.txt` - Python依赖列表
- `ShopSetup.spec` - PyInstaller配置文件（自动生成）

## 📞 技术支持

如遇到打包问题，请：
1. 检查Python和PyInstaller版本
2. 确认所有必要文件存在
3. 查看详细错误信息
4. 联系技术支持并提供错误日志

---

**更新日期**: 2024-12-01  
**版本**: 2.0  
**支持平台**: Windows 10/11, macOS 10.14+
