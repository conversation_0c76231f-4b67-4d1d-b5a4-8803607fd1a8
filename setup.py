#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
紫鸟浏览器RPA框架安装脚本
"""

import os
import sys
import subprocess
import platform

def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ 需要Python 3.8或更高版本")
        print(f"当前版本: {version.major}.{version.minor}.{version.micro}")
        return False
    print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    return True

def check_system():
    """检查操作系统"""
    system = platform.system()
    if system not in ['Windows', 'Darwin']:
        print(f"❌ 不支持的操作系统: {system}")
        print("只支持Windows和macOS")
        return False
    print(f"✅ 操作系统: {system}")
    return True

def install_dependencies(minimal=False):
    """安装依赖包"""
    print("\n📦 安装依赖包...")
    
    requirements_file = "requirements-minimal.txt" if minimal else "requirements.txt"
    
    if not os.path.exists(requirements_file):
        print(f"❌ 找不到 {requirements_file} 文件")
        return False
    
    try:
        cmd = [sys.executable, "-m", "pip", "install", "-r", requirements_file]
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 依赖包安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def create_directories():
    """创建必要的目录"""
    print("\n📁 创建目录结构...")
    
    directories = [
        "webdriver",
        "logs", 
        "uploads",
        "configs"
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"✅ 创建目录: {directory}")
        else:
            print(f"📁 目录已存在: {directory}")

def download_chromedriver():
    """下载ChromeDriver"""
    print("\n🔄 下载ChromeDriver...")
    
    try:
        from ziniao_rpa_base import download_driver, get_driver_folder_path
        driver_folder = get_driver_folder_path()
        download_driver(driver_folder)
        print("✅ ChromeDriver下载完成")
        return True
    except Exception as e:
        print(f"⚠️ ChromeDriver下载失败: {e}")
        print("可以稍后手动下载或在运行时自动下载")
        return False

def run_test():
    """运行测试"""
    print("\n🧪 运行基础测试...")
    
    try:
        # 测试导入核心模块
        from ziniao_rpa_base import BaseZiniaoRPA
        from gui_framework import ConfigurableGUI, ComponentConfig
        print("✅ 核心模块导入成功")
        
        # 测试GUI框架
        print("✅ GUI框架测试通过")
        
        return True
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主安装流程"""
    print("🚀 紫鸟浏览器RPA框架安装程序")
    print("=" * 50)
    
    # 检查系统要求
    if not check_python_version():
        return False
    
    if not check_system():
        return False
    
    # 询问安装选项
    print("\n📋 选择安装选项:")
    print("1. 完整安装 (包含所有可选依赖)")
    print("2. 最小安装 (只安装核心依赖)")
    
    while True:
        choice = input("\n请选择 (1/2): ").strip()
        if choice in ['1', '2']:
            break
        print("请输入 1 或 2")
    
    minimal = choice == '2'
    
    # 安装依赖
    if not install_dependencies(minimal):
        return False
    
    # 创建目录
    create_directories()
    
    # 下载ChromeDriver
    download_chromedriver()
    
    # 运行测试
    if not run_test():
        print("\n⚠️ 测试失败，但基础安装可能已完成")
    
    print("\n🎉 安装完成！")
    print("\n📚 使用说明:")
    print("1. 基础示例: python amazon_upload_example.py")
    print("2. 可配置GUI: python amazon_upload_configurable.py")
    print("3. JSON配置: python json_gui_loader.py")
    print("4. 开发模板: python rpa_template.py")
    
    print("\n📖 详细文档请查看 README.md")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n⚠️ 安装被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 安装过程中发生错误: {e}")
        sys.exit(1) 