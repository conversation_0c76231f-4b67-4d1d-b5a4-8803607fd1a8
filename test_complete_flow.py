#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完整的Chrome版本检测和ChromeDriver下载流程
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from script.shopSetup import ShopSetup

def test_complete_webdriver_flow():
    """测试完整的WebDriver初始化流程"""
    print("=" * 60)
    print("测试完整的WebDriver初始化流程")
    print("=" * 60)
    
    shop_setup = ShopSetup()
    
    # 模拟不同的场景
    test_scenarios = [
        {
            "name": "场景1: 紫鸟返回正确版本信息",
            "open_ret_json": {
                "core_type": "Chromium",
                "core_version": "138.0.7204.169",
                "debuggingPort": 48755
            }
        },
        {
            "name": "场景2: 紫鸟返回错误版本信息",
            "open_ret_json": {
                "core_type": "Chromium", 
                "core_version": "131.0.6778.76",
                "debuggingPort": 48755
            }
        },
        {
            "name": "场景3: 紫鸟未返回版本信息",
            "open_ret_json": {
                "core_type": "Chromium",
                "debuggingPort": 48755
            }
        }
    ]
    
    for scenario in test_scenarios:
        print(f"\n{scenario['name']}")
        print("-" * 50)
        
        open_ret_json = scenario['open_ret_json']
        
        # 模拟版本检测逻辑
        print("🔍 步骤1: Chrome版本检测")
        
        # 优先从系统检测Chrome版本（更准确）
        system_version = shop_setup.chrome_driver_manager.get_system_chrome_version()
        if system_version:
            core_version = f"{system_version}.0.0.0"
            print(f"✅ 系统检测到Chrome版本: {core_version}")
        else:
            # 系统检测失败，尝试从紫鸟返回的信息获取
            core_version = open_ret_json.get('core_version')
            if core_version:
                print(f"⚠️ 系统检测失败，使用紫鸟返回的版本: {core_version}")
            else:
                print("⚠️ 所有版本检测都失败，使用默认版本138")
                core_version = '138.0.0.0'
        
        major_version = core_version.split('.')[0]
        print(f"📋 使用Chrome主版本: {major_version}")
        
        # 步骤2: 检查ChromeDriver是否存在
        print("\n🔍 步骤2: ChromeDriver检查")
        import platform
        if platform.system() == 'Windows':
            chrome_driver_path = os.path.join(shop_setup.driver_folder_path, f'chromedriver{major_version}.exe')
        else:
            chrome_driver_path = os.path.join(shop_setup.driver_folder_path, f'chromedriver{major_version}')
        
        print(f"🔍 查找路径: {chrome_driver_path}")
        
        if os.path.exists(chrome_driver_path):
            print(f"✅ 找到匹配的ChromeDriver: {chrome_driver_path}")
        else:
            print(f"❌ 未找到ChromeDriver {major_version}")
            
            # 步骤3: 检查是否可以下载
            print("\n🔍 步骤3: 下载检查")
            if major_version in shop_setup.chrome_driver_manager.version_mapping:
                mapped_version = shop_setup.chrome_driver_manager.version_mapping[major_version]
                print(f"✅ 找到版本映射: Chrome {major_version} -> ChromeDriver {mapped_version}")
                print(f"📥 可以下载ChromeDriver {major_version}")
            else:
                print(f"⚠️ 版本映射中没有Chrome {major_version}")
                print("🔍 尝试动态获取版本映射...")
                # 这里可以测试动态获取，但为了避免网络请求，我们跳过
                print("⚠️ 动态获取需要网络连接，跳过测试")
            
            # 步骤4: 查找替代版本
            print("\n🔍 步骤4: 查找替代版本")
            available_drivers = shop_setup._find_available_chrome_drivers()
            if available_drivers:
                print(f"📋 找到可用的ChromeDriver: {[os.path.basename(d) for d in available_drivers]}")
                
                # 查找最佳匹配
                best_match = shop_setup._find_best_matching_driver(major_version, available_drivers)
                if best_match:
                    match_version = shop_setup._extract_version_from_path(best_match)
                    version_diff = abs(int(major_version) - match_version)
                    print(f"✅ 最佳匹配: {os.path.basename(best_match)} (版本: {match_version}, 差异: {version_diff})")
                    
                    if version_diff <= 3:  # 版本差异在3以内认为可接受
                        print(f"✅ 版本差异可接受，可以使用替代版本")
                    else:
                        print(f"⚠️ 版本差异较大，可能存在兼容性问题")
                else:
                    print("❌ 未找到最佳匹配")
            else:
                print("❌ 未找到任何可用的ChromeDriver")
        
        # 步骤5: 总结建议
        print("\n📋 步骤5: 处理建议")
        if os.path.exists(chrome_driver_path):
            print("✅ 建议: 直接使用匹配的ChromeDriver")
        elif major_version in shop_setup.chrome_driver_manager.version_mapping:
            print("📥 建议: 下载对应版本的ChromeDriver")
        elif available_drivers:
            best_match = shop_setup._find_best_matching_driver(major_version, available_drivers)
            if best_match:
                match_version = shop_setup._extract_version_from_path(best_match)
                print(f"🔄 建议: 使用替代版本 ChromeDriver {match_version}")
            else:
                print("❌ 建议: 手动下载合适的ChromeDriver版本")
        else:
            print("❌ 建议: 检查网络连接并重新下载ChromeDriver")

def test_version_mapping():
    """测试版本映射完整性"""
    print("\n" + "=" * 60)
    print("测试版本映射完整性")
    print("=" * 60)
    
    shop_setup = ShopSetup()
    version_mapping = shop_setup.chrome_driver_manager.version_mapping
    
    print("📋 当前支持的Chrome版本:")
    for chrome_version, driver_version in sorted(version_mapping.items(), key=lambda x: int(x[0]), reverse=True):
        print(f"  Chrome {chrome_version} -> ChromeDriver {driver_version}")
    
    # 检查常见版本是否都有映射
    common_versions = ["131", "132", "133", "134", "135", "136", "137", "138"]
    print("\n🔍 检查常见版本映射:")
    for version in common_versions:
        if version in version_mapping:
            print(f"✅ Chrome {version}: {version_mapping[version]}")
        else:
            print(f"❌ Chrome {version}: 缺少映射")

if __name__ == "__main__":
    try:
        test_complete_webdriver_flow()
        test_version_mapping()
        print("\n" + "=" * 60)
        print("✅ 完整流程测试完成")
        print("=" * 60)
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
