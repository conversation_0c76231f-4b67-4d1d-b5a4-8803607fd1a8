#!/bin/bash
# 店铺设置工具 - macOS 打包脚本（优化版）

echo "========================================"
echo "     店铺设置工具 - macOS 打包"
echo "========================================"
echo

# 检查Python环境
echo "正在检查Python环境..."
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3环境"
    echo "请安装Python 3.8+:"
    echo "  方法1: brew install python3"
    echo "  方法2: https://www.python.org/downloads/"
    exit 1
fi

PYTHON_VERSION=$(python3 --version)
echo "✅ $PYTHON_VERSION"

# 检查Python版本
PYTHON_MAJOR=$(python3 -c "import sys; print(sys.version_info.major)")
PYTHON_MINOR=$(python3 -c "import sys; print(sys.version_info.minor)")
if [ "$PYTHON_MAJOR" -lt 3 ] || ([ "$PYTHON_MAJOR" -eq 3 ] && [ "$PYTHON_MINOR" -lt 8 ]); then
    echo "错误: 需要Python 3.8或更高版本，当前版本: $PYTHON_VERSION"
    exit 1
fi

# 检查PyInstaller
echo
echo "正在检查PyInstaller..."
if ! python3 -c "import PyInstaller" 2>/dev/null; then
    echo "正在安装PyInstaller..."
    pip3 install PyInstaller
    if [ $? -ne 0 ]; then
        echo "错误: PyInstaller安装失败"
        echo "请尝试: python3 -m pip install --upgrade pip"
        echo "然后重新运行此脚本"
        exit 1
    fi
else
    PYINSTALLER_VERSION=$(python3 -c "import PyInstaller; print(PyInstaller.__version__)")
    echo "✅ PyInstaller版本: $PYINSTALLER_VERSION"
fi

# 检查必要的依赖
echo
echo "正在检查必要依赖..."
REQUIRED_MODULES=("selenium" "requests" "pandas" "tkinter")
MISSING_MODULES=()

for module in "${REQUIRED_MODULES[@]}"; do
    if python3 -c "import $module" 2>/dev/null; then
        echo "✅ $module"
    else
        echo "❌ $module (缺失)"
        MISSING_MODULES+=("$module")
    fi
done

# 安装缺失的模块
if [ ${#MISSING_MODULES[@]} -gt 0 ]; then
    echo
    echo "正在安装缺失的依赖..."
    for module in "${MISSING_MODULES[@]}"; do
        echo "安装 $module..."
        if [ "$module" = "tkinter" ]; then
            echo "注意: tkinter是Python内置模块，如果缺失请重新安装Python"
        else
            pip3 install "$module"
        fi
    done
fi

# 检查必要文件
echo
echo "正在检查必要文件..."
REQUIRED_FILES=(
    "script/shopSetup.py"
    "ziniao_rpa_base.py"
    "gui_framework.py"
    "gui_utils.py"
    "global_logger.py"
    "build_cross_platform.py"
)

for file in "${REQUIRED_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file"
    else
        echo "❌ 错误: 找不到 $file 文件"
        exit 1
    fi
done

# 开始打包
echo
echo "========================================"
echo "开始macOS版本打包..."
echo "========================================"
echo

# 设置环境变量
export PYTHONPATH=".:$PYTHONPATH"

# 执行构建
python3 build_cross_platform.py

BUILD_RESULT=$?

echo
if [ $BUILD_RESULT -eq 0 ]; then
    echo "========================================"
    echo "          打包成功完成！"
    echo "========================================"
    echo

    # 检查生成的文件
    if [ -d "ShopSetup_macOS" ]; then
        echo "生成的文件："
        echo "📁 ShopSetup_macOS/"
        ls -la ShopSetup_macOS/
        echo

        # 检查文件大小
        if [ -f "ShopSetup_macOS/店铺设置" ]; then
            FILE_SIZE=$(ls -lh "ShopSetup_macOS/店铺设置" | awk '{print $5}')
            echo "📊 可执行文件大小: $FILE_SIZE"
        fi

        echo
        echo "🎉 macOS版本构建完成！"
        echo
        echo "📋 使用说明："
        echo "1. 将 ShopSetup_macOS 文件夹复制到目标Mac电脑"
        echo "2. 双击 '启动店铺设置.command' 运行程序"
        echo "3. 首次运行可能需要在'系统偏好设置 > 安全性与隐私'中允许"
        echo "4. 详细说明请查看 README.txt"
        echo
    else
        echo "警告: 未找到生成的 ShopSetup_macOS 目录"
        exit 1
    fi
else
    echo "========================================"
    echo "          打包失败！"
    echo "========================================"
    echo
    echo "🔧 故障排除建议："
    echo "1. 检查Python版本是否为3.8+"
    echo "2. 确认所有依赖都已正确安装"
    echo "3. 检查是否有权限问题"
    echo "4. 查看上面的详细错误信息"
    echo
    echo "💡 常见解决方案："
    echo "- 升级pip: python3 -m pip install --upgrade pip"
    echo "- 重新安装PyInstaller: pip3 install --force-reinstall PyInstaller"
    echo "- 检查网络连接"
    echo
    exit 1
fi
