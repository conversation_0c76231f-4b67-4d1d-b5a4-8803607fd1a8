"""
GUI配置工具方法
"""

import json
import os
import tkinter as tk
from tkinter import messagebox


def save_config_data(variable_manager, silent=False):
    """保存配置数据"""
    from ziniao_rpa_base import get_config_path
    
    # 收集所有变量的值
    config_data = {}
    
    # 处理基本变量
    basic_vars = {
        'client_path': 'client_path',
        'driver_path': 'driver_folder_path',
        'company': 'user_info.company',
        'username': 'user_info.username',
        'password': 'user_info.password',
        'upload_folder': 'upload_folder'
        # 已移除无头模式配置（紫鸟浏览器不支持）
    }
    
    config_data['user_info'] = {}
    
    for var_name, config_key in basic_vars.items():
        var = variable_manager.get_variable(var_name)
        if var:
            value = var.get()
            if config_key.startswith('user_info.'):
                key = config_key.split('.')[1]
                config_data['user_info'][key] = value
            else:
                config_data[config_key] = value
    
    # 处理国家选择
    countries_var = variable_manager.get_variable('countries')
    if countries_var:
        selected_countries = [country for country, var in countries_var.items() if var.get()]
        config_data['selected_countries'] = selected_countries
    
    try:
        config_path = get_config_path()
        with open(config_path, "w", encoding="utf-8") as f:
            json.dump(config_data, f, ensure_ascii=False, indent=4)
        if not silent:
            messagebox.showinfo("成功", "配置已保存")
        return True
    except Exception as e:
        if not silent:
            messagebox.showerror("错误", f"保存配置失败: {str(e)}")
        return False


def load_config_data(variable_manager):
    """加载配置数据"""
    from ziniao_rpa_base import get_config_path
    
    try:
        config_path = get_config_path()
        if os.path.exists(config_path):
            with open(config_path, "r", encoding="utf-8") as f:
                config = json.load(f)
            
            # 加载基本配置
            basic_mappings = {
                'client_path': 'client_path',
                'driver_path': 'driver_folder_path',
                'upload_folder': 'upload_folder'
                # 已移除无头模式配置（紫鸟浏览器不支持）
            }
            
            for var_name, config_key in basic_mappings.items():
                var = variable_manager.get_variable(var_name)
                if var and config_key in config:
                    var.set(config[config_key])
            
            # 加载用户信息
            user_info = config.get('user_info', {})
            user_mappings = {
                'company': 'company',
                'username': 'username', 
                'password': 'password'
            }
            
            for var_name, info_key in user_mappings.items():
                var = variable_manager.get_variable(var_name)
                if var and info_key in user_info:
                    var.set(user_info[info_key])
            
            # 加载国家选择
            countries_var = variable_manager.get_variable('countries')
            if countries_var:
                selected_countries = config.get('selected_countries', [])
                for country in selected_countries:
                    if country in countries_var:
                        countries_var[country].set(True)
                        
    except Exception as e:
        pass  # 配置文件可能损坏或不完整


def validate_basic_config(variable_manager):
    """验证基本配置"""
    client_path = variable_manager.get_variable('client_path')
    company = variable_manager.get_variable('company')
    username = variable_manager.get_variable('username')
    password = variable_manager.get_variable('password')
    
    if not client_path or not client_path.get():
        messagebox.showerror("错误", "请设置紫鸟浏览器路径")
        return False
    
    if not all([company and company.get(), username and username.get(), password and password.get()]):
        messagebox.showerror("错误", "请填写完整的用户信息")
        return False
    
    return True


def setup_rpa_instance(rpa_instance, variable_manager):
    """设置RPA实例参数"""
    from ziniao_rpa_base import get_driver_folder_path
    
    client_path = variable_manager.get_variable('client_path')
    driver_path = variable_manager.get_variable('driver_path')
    company = variable_manager.get_variable('company')
    username = variable_manager.get_variable('username')
    password = variable_manager.get_variable('password')
    
    rpa_instance.client_path = client_path.get()
    
    rpa_instance.driver_folder_path = driver_path.get() if driver_path and driver_path.get() else get_driver_folder_path()
    if not os.path.exists(rpa_instance.driver_folder_path):
        os.makedirs(rpa_instance.driver_folder_path)
    
    rpa_instance.user_info = {
        "company": company.get(),
        "username": username.get(),
        "password": password.get()
    } 