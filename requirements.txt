# Ziniao RPA Framework Dependencies

# Core dependencies
requests==2.31.0
selenium==4.15.0
pyautogui==0.9.54
pyperclip==1.8.2
pywin32==306

# Optional dependencies for data processing
pandas==2.1.3
openpyxl==3.1.2

# Optional dependencies for enhanced features
loguru==0.7.2
pyyaml==6.0.1
pillow==10.1.0
PyInstaller
psutil

# Note: The following are Python built-in modules (no installation needed):
# - tkinter (GUI framework)
# - platform, os, sys, subprocess, time, traceback, uuid, json, hashlib, shutil
# - abc (abstract base classes)