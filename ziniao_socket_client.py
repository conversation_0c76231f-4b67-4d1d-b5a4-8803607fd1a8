#!/usr/bin/python
# -*- coding: UTF-8 -*-

"""
紫鸟浏览器Socket通信客户端
实现与紫鸟浏览器主进程的真正Socket通信
基于官方文档：https://www.cnblogs.com/skyxing7/p/14929290.html
"""

import socket
import json
import time
import uuid
import subprocess
import os
from global_logger import log_info, log_error, log_success, log_warning, log_step


class ZiniaoSocketClient:
    """紫鸟浏览器Socket通信客户端（真正的Socket实现）"""
    
    def __init__(self, host='127.0.0.1', port=9222, buf_size=8192):
        """
        初始化Socket客户端

        :param host: 紫鸟浏览器主进程地址
        :param port: Socket通信端口（注意：与HTTP端口不同）
        :param buf_size: 接收缓冲区大小
        """
        self.host = host
        self.port = port
        self.buf_size = buf_size
        self.socket = None
        self.is_connected = False

        # 用户信息（需要从配置中获取）
        self.user_info = {}
        
    def set_user_info(self, company, username, password):
        """设置用户登录信息"""
        self.user_info = {
            "company": company,
            "username": username,
            "password": password
        }
        log_info(f"已设置用户信息: {company}/{username}")
    
    def connect(self, timeout=10):
        """
        建立Socket连接

        :param timeout: 连接超时时间
        :return: 连接是否成功
        """
        try:
            log_step(f"正在连接紫鸟浏览器Socket服务 {self.host}:{self.port}...")

            # 创建TCP Socket连接
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(timeout)
            self.socket.connect((self.host, self.port))

            self.is_connected = True
            log_success(f"✅ Socket连接成功: {self.host}:{self.port}")
            return True

        except Exception as e:
            log_error(f"❌ Socket连接失败: {str(e)}")
            self.is_connected = False
            if self.socket:
                try:
                    self.socket.close()
                except:
                    pass
                self.socket = None
            return False

    def disconnect(self):
        """断开Socket连接"""
        try:
            if self.socket:
                self.socket.close()
                self.socket = None
            self.is_connected = False
            log_info("Socket连接已断开")
        except Exception as e:
            log_warning(f"断开连接时发生错误: {str(e)}")
    
    def send_command(self, action, params=None, retry_count=1):
        """
        发送Socket命令到紫鸟浏览器（按照官方文档格式）

        :param action: 操作类型 (getBrowserList, startBrowser, stopBrowser等)
        :param params: 命令参数
        :param retry_count: 重试次数
        :return: 响应结果
        """
        if not self.is_connected:
            log_warning("Socket未连接，尝试重新连接...")
            if not self.connect():
                log_error("Socket重连失败，无法发送命令")
                return None

        for attempt in range(retry_count + 1):
            try:
                # 构建请求数据（按照官方文档格式）
                request_data = {
                    "userInfo": json.dumps(self.user_info),  # 用户信息需要JSON字符串
                    "action": action,
                    "requestId": str(uuid.uuid4())
                }

                log_info(f"用户信息: {self.user_info}")
                log_info(f"请求数据: {request_data}")

                # 添加额外参数
                if params:
                    request_data.update(params)

                log_info(f"📤 发送Socket命令: {action}")

                # 发送Socket请求（必须以\r\n结尾）
                request_str = json.dumps(request_data) + '\r\n'
                self.socket.send(request_str.encode('utf-8'))

                # 设置接收超时时间（保守优化：15秒）
                self.socket.settimeout(15)

                # 接收响应
                response = self.socket.recv(self.buf_size)
                response_str = response.decode('utf-8').strip()

                # 检查响应是否为空
                if not response_str:
                    log_error("收到空响应")
                    return None

                # 解析JSON响应
                try:
                    response_data = json.loads(response_str)
                    log_info(f"📥 收到Socket响应: statusCode={response_data.get('statusCode', 'unknown')}")
                    return response_data
                except json.JSONDecodeError as e:
                    log_error(f"JSON解析失败: {str(e)}")
                    log_error(f"原始响应: {response_str[:200]}...")
                    return None

            except Exception as e:
                log_error(f"发送Socket命令失败 (尝试 {attempt + 1}/{retry_count + 1}): {str(e)}")

                # 如果是连接错误且还有重试机会，尝试重连
                if attempt < retry_count and ("连接" in str(e) or "10053" in str(e) or "10054" in str(e)):
                    log_warning("检测到连接问题，尝试重新连接...")
                    self.is_connected = False
                    if self.connect():
                        log_info("重连成功，继续重试...")
                        continue
                    else:
                        log_error("重连失败")
                        break
                else:
                    break

        return None
    
    def get_browser_list(self):
        """
        获取店铺列表
        
        :return: 店铺列表或None
        """
        log_step("正在获取店铺列表...")

        response = self.send_command("getBrowserList", retry_count=2)
        
        if response and response.get('statusCode') == 0:
            browser_list = response.get('browserList', [])
            log_success(f"✅ 获取到 {len(browser_list)} 个店铺")
            
            # 打印店铺信息
            for browser in browser_list:
                log_info(f"  店铺: {browser.get('browserName')} (ID: {browser.get('browserOauth')})")
            
            return browser_list
        else:
            error_msg = self.get_error_message(response.get('statusCode') if response else -1)
            log_error(f"❌ 获取店铺列表失败: {error_msg}")
            return None
    
    def start_browser(self, browser_oauth, is_headless=False):
        """
        启动指定店铺
        
        :param browser_oauth: 店铺ID
        :param is_headless: 是否无头模式
        :return: 启动结果包含launcherPage和debuggingPort
        """
        log_step(f"正在启动店铺: {browser_oauth} (无头模式: {is_headless})")
        
        params = {
            "browserOauth": browser_oauth,
            "isHeadless": is_headless
        }
        
        response = self.send_command("startBrowser", params, retry_count=2)
        
        if response and response.get('statusCode') == 0:
            result = {
                "launcherPage": response.get('launcherPage'),
                "debuggingPort": response.get('debuggingPort'),
                "browserOauth": browser_oauth
            }
            log_success(f"✅ 店铺启动成功")
            log_info(f"  启动页面: {result['launcherPage']}")
            log_info(f"  调试端口: {result['debuggingPort']}")
            return result
        else:
            error_msg = self.get_error_message(response.get('statusCode') if response else -1)
            log_error(f"❌ 店铺启动失败: {error_msg}")
            return None
    
    def stop_browser(self, browser_oauth):
        """
        关闭指定店铺
        
        :param browser_oauth: 店铺ID
        :return: 是否成功
        """
        log_step(f"正在关闭店铺: {browser_oauth}")
        
        params = {
            "browserOauth": browser_oauth
        }
        
        response = self.send_command("stopBrowser", params, retry_count=2)
        
        if response and response.get('statusCode') == 0:
            log_success(f"✅ 店铺关闭成功")
            return True
        else:
            error_msg = self.get_error_message(response.get('statusCode') if response else -1)
            log_error(f"❌ 店铺关闭失败: {error_msg}")
            return False
    
    def get_error_message(self, status_code):
        """
        获取错误码对应的错误信息
        
        :param status_code: 状态码
        :return: 错误信息
        """
        error_messages = {
            -10000: "未知异常",
            -10001: "内核窗口创建失败", 
            -10002: "Socket参数非法",
            -10003: "登录失败",
            -10004: "browserOauth缺失",
            -10005: "店铺启动中，请稍后重试",
            1: "初始化数据失败",
            2: "代理IP无法使用",
            5: "初始化代理失败",
            7: "启动内核失败"
        }
        return error_messages.get(status_code, f"未知错误码: {status_code}")
    
    def ensure_ziniao_process_running(self, ziniao_path):
        """
        确保紫鸟浏览器主进程正在运行（Socket模式）

        :param ziniao_path: 紫鸟浏览器可执行文件路径
        :return: 是否成功启动
        """
        try:
            # 检查进程是否已经运行
            if self.is_connected or self.connect():
                log_info("紫鸟浏览器Socket服务已在运行")
                return True

            # 先清理现有的紫鸟进程（避免冲突）
            log_step("正在清理现有的紫鸟浏览器进程...")
            try:
                subprocess.run(['taskkill', '/f', '/im', 'superbrowser.exe'],
                             capture_output=True, timeout=10)
                subprocess.run(['taskkill', '/f', '/im', 'starter.exe'],
                             capture_output=True, timeout=10)
                log_info("已清理现有的紫鸟浏览器进程")
                time.sleep(2)  # 等待进程完全关闭
            except:
                pass

            # 启动紫鸟浏览器主进程（Socket模式）
            log_step("正在启动紫鸟浏览器主进程...")

            if not os.path.exists(ziniao_path):
                log_error(f"紫鸟浏览器路径不存在: {ziniao_path}")
                return False

            cmd = f'"{ziniao_path}" --run_type=web_driver --socket_port={self.port}'
            log_info(f"启动命令: {cmd}")

            subprocess.Popen(cmd, shell=True)

            # 等待进程启动
            for i in range(15):  # 增加等待时间到15秒
                time.sleep(1)
                if self.connect():
                    log_success("✅ 紫鸟浏览器Socket服务启动成功")
                    return True
                log_info(f"等待Socket服务启动... ({i+1}/15)")

            log_error("❌ 紫鸟浏览器Socket服务启动超时")
            return False

        except Exception as e:
            log_error(f"启动紫鸟浏览器Socket服务失败: {str(e)}")
            return False
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.disconnect()


# 使用示例
if __name__ == "__main__":
    # 创建Socket客户端
    client = ZiniaoSocketClient()
    
    # 设置用户信息
    client.set_user_info("测试公司", "测试用户", "测试密码")
    
    try:
        # 连接到紫鸟浏览器
        if client.connect():
            # 获取店铺列表
            browsers = client.get_browser_list()
            
            if browsers:
                # 启动第一个店铺
                first_browser = browsers[0]
                result = client.start_browser(first_browser['browserOauth'], is_headless=True)
                
                if result:
                    print(f"店铺启动成功，调试端口: {result['debuggingPort']}")
                    
                    # 等待一段时间后关闭
                    time.sleep(5)
                    client.stop_browser(first_browser['browserOauth'])
    
    finally:
        # 断开连接
        client.disconnect()
