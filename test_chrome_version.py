#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Chrome版本检测和ChromeDriver匹配功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ziniao_rpa_base import ChromeDriverManager
from script.shopSetup import ShopSetup

def test_chrome_version_detection():
    """测试Chrome版本检测功能"""
    print("=" * 60)
    print("测试Chrome版本检测功能")
    print("=" * 60)
    
    # 创建ChromeDriverManager实例
    manager = ChromeDriverManager("./drivers")
    
    # 测试系统Chrome版本检测
    print("\n1. 测试系统Chrome版本检测:")
    system_version = manager.get_system_chrome_version()
    if system_version:
        print(f"✅ 检测到系统Chrome版本: {system_version}")
    else:
        print("❌ 无法检测系统Chrome版本")
    
    # 测试版本映射
    print("\n2. 测试版本映射:")
    for version in ["131", "134", "138"]:
        mapped_version = manager.version_mapping.get(version)
        if mapped_version:
            print(f"✅ Chrome {version} -> ChromeDriver {mapped_version}")
        else:
            print(f"❌ Chrome {version} 没有对应的ChromeDriver版本")
    
    # 测试可用ChromeDriver查找
    print("\n3. 测试可用ChromeDriver查找:")
    shop_setup = ShopSetup()
    available_drivers = shop_setup._find_available_chrome_drivers()
    if available_drivers:
        print(f"✅ 找到可用的ChromeDriver:")
        for driver in available_drivers:
            version = shop_setup._extract_version_from_path(driver)
            print(f"   - {os.path.basename(driver)} (版本: {version})")
    else:
        print("❌ 未找到任何可用的ChromeDriver")
    
    # 测试最佳匹配查找
    print("\n4. 测试最佳匹配查找:")
    if available_drivers:
        target_versions = ["131", "134", "138"]
        for target in target_versions:
            best_match = shop_setup._find_best_matching_driver(target, available_drivers)
            if best_match:
                match_version = shop_setup._extract_version_from_path(best_match)
                print(f"✅ 目标版本 {target} -> 最佳匹配: {os.path.basename(best_match)} (版本: {match_version})")
            else:
                print(f"❌ 目标版本 {target} 没有找到匹配的ChromeDriver")

def test_webdriver_initialization():
    """测试WebDriver初始化"""
    print("\n" + "=" * 60)
    print("测试WebDriver初始化")
    print("=" * 60)
    
    # 模拟open_ret_json数据
    test_cases = [
        {
            "name": "有版本信息的情况",
            "data": {
                "core_type": "Chromium",
                "core_version": "131.0.6778.76",
                "debuggingPort": 48755
            }
        },
        {
            "name": "无版本信息的情况",
            "data": {
                "core_type": "Chromium",
                "debuggingPort": 48755
            }
        }
    ]
    
    shop_setup = ShopSetup()
    
    for test_case in test_cases:
        print(f"\n测试场景: {test_case['name']}")
        print("-" * 40)
        
        # 模拟版本检测逻辑
        open_ret_json = test_case['data']
        core_version = open_ret_json.get('core_version')
        
        if not core_version:
            print("⚠️ 未获取到Chrome版本信息，尝试从系统检测...")
            system_version = shop_setup.chrome_driver_manager.get_system_chrome_version()
            if system_version:
                core_version = f"{system_version}.0.0.0"
                print(f"✅ 从系统检测到Chrome版本: {core_version}")
            else:
                print("⚠️ 系统检测也失败，使用默认版本131")
                core_version = '131.0.0.0'
        
        major_version = core_version.split('.')[0]
        print(f"📋 使用Chrome版本: {core_version}, 主版本号: {major_version}")
        
        # 检查ChromeDriver路径
        chrome_driver_path = os.path.join(shop_setup.driver_folder_path, f'chromedriver{major_version}.exe')
        print(f"🔍 查找ChromeDriver路径: {chrome_driver_path}")
        
        if os.path.exists(chrome_driver_path):
            print(f"✅ 找到匹配的ChromeDriver: {chrome_driver_path}")
        else:
            print(f"❌ 未找到匹配的ChromeDriver，尝试查找替代版本...")
            available_drivers = shop_setup._find_available_chrome_drivers()
            if available_drivers:
                best_match = shop_setup._find_best_matching_driver(major_version, available_drivers)
                if best_match:
                    match_version = shop_setup._extract_version_from_path(best_match)
                    print(f"✅ 找到最佳匹配: {os.path.basename(best_match)} (版本: {match_version})")
                else:
                    print(f"⚠️ 使用第一个可用版本: {os.path.basename(available_drivers[0])}")

if __name__ == "__main__":
    try:
        test_chrome_version_detection()
        test_webdriver_initialization()
        print("\n" + "=" * 60)
        print("✅ 测试完成")
        print("=" * 60)
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
