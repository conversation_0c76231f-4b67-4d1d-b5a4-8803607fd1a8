@echo off
chcp 65001 >nul
title ShopSetup 打包工具

echo ========================================
echo      ShopSetup 一键打包工具
echo ========================================
echo.

echo 正在检查Python环境...
python --version
if %errorlevel% neq 0 (
    echo 错误: 未找到Python环境，请先安装Python
    pause
    exit /b 1
)

echo.
echo 正在检查必要文件...
if not exist "script\shopSetup.py" (
    echo 错误: 找不到 script\shopSetup.py 文件
    pause
    exit /b 1
)

if not exist "ziniao_rpa_base.py" (
    echo 错误: 找不到 ziniao_rpa_base.py 文件
    pause
    exit /b 1
)

echo.
echo 开始打包流程...
python build_exe.py

echo.
echo 打包完成！请检查生成的文件：
echo - dist\ShopSetup.exe
echo - ShopSetup_Portable\ (推荐使用)
echo.

pause 