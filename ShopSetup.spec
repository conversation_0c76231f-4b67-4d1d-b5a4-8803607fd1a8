# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['script\\shopSetup.py'],
    pathex=[],
    binaries=[],
    datas=[('ziniao_config.json', '.'), ('gui_framework.py', '.'), ('gui_utils.py', '.'), ('ziniao_rpa_base.py', '.'), ('ziniao_socket_client.py', '.'), ('global_logger.py', '.'), ('rpa_template.py', '.')],
    hiddenimports=['selenium', 'selenium.webdriver', 'selenium.webdriver.chrome', 'selenium.webdriver.common.by', 'selenium.webdriver.support.ui', 'selenium.webdriver.support.expected_conditions', 'selenium.webdriver.common.action_chains', 'selenium.webdriver.common.keys', 'selenium.webdriver.chrome.service', 'selenium.webdriver.chrome.options', 'selenium.common.exceptions', 'selenium.common', 'pyautogui', 'pyperclip', 'requests', 'pandas', 'openpyxl', 'loguru', 'yaml', 'PIL', 'tkinter', 'tkinter.ttk', 'tkinter.messagebox', 'tkinter.filedialog', 'tkinter.scrolledtext', 'traceback', 'win32gui', 'win32con', 'win32api', 'win32process', 'win32clipboard', 'pywintypes', 'pythoncom', 'win32com', 'win32com.client', 'global_logger', 'gui_framework', 'gui_utils', 'ziniao_rpa_base', 'ziniao_socket_client', 'rpa_template'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='ShopSetup',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
