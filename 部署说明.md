# ShopSetup 程序部署和使用说明

## 📋 打包完成文件列表

✅ **已生成的文件：**
- `dist/ShopSetup.exe` (50MB) - 单个exe文件
- `ShopSetup_Portable/` - 便携版包（推荐使用）
  - `ShopSetup.exe` (50MB) - 主程序
  - `ziniao_config.json` - 配置文件
  - `README.txt` - 使用说明

## 🚀 部署到其他电脑

### 方式一：使用便携版包（推荐）
1. 将整个 `ShopSetup_Portable` 文件夹复制到目标电脑
2. 无需安装，直接双击 `ShopSetup.exe` 运行

### 方式二：使用单个exe文件
1. 复制 `dist/ShopS[AugmentWebviewStateStore.xml](.idea/AugmentWebviewStateStore.xml)etup.exe` 到目标电脑
2. 同时复制 `ziniao_config.json` 配置文件到相同目录
3. 双击exe文件运行

## 💻 目标电脑运行要求

### 必需环境：
- ✅ Windows 10/11 操作系统
- ✅ Chrome 浏览器（最新版本）
- ✅ 稳定的网络连接

### 可选但推荐：
- 🔄 ChromeDriver（程序会自动检测和下载）
- 📂 创建专用文件夹存放程序

## 🛠️ 首次运行配置

1. **创建工作目录**
   ```
   C:\ShopSetup\
   ├── ShopSetup.exe
   ├── ziniao_config.json
   └── logs\ (自动创建)
   ```

2. **检查配置文件**
   - 编辑 `ziniao_config.json` 确保设置正确
   - 验证店铺信息和账户配置

3. **防病毒软件设置**
   - 将程序目录添加到杀毒软件白名单
   - 避免误报导致程序无法正常运行

## ⚠️ 注意事项

### 启动相关：
- 🕐 首次启动可能需要1-2分钟（解压内嵌文件）
- 🖥️ 程序会自动打开GUI界面
- 📊 启动过程中可能有短暂黑屏，属正常现象

### 运行相关：
- 🌐 确保网络畅通，程序需要访问亚马逊网站
- 🔧 如果Chrome版本过旧，程序会提示更新
- 💾 日志文件自动保存到程序目录的logs文件夹

### 兼容性：
- ✅ 支持 Windows 10 (1909+) 和 Windows 11
- ✅ 兼容最新版Chrome浏览器
- ❌ 不支持Windows 7/8.1

## 🔧 故障排除

### 常见问题：

1. **程序无法启动**
   - 检查是否被杀毒软件拦截
   - 确认Windows版本兼容性
   - 尝试以管理员身份运行

2. **Chrome相关错误**
   - 更新Chrome到最新版本
   - 检查ChromeDriver是否正确下载
   - 重启程序重新检测

3. **网络连接问题**
   - 检查防火墙设置
   - 确认网络连接稳定
   - 尝试更换网络环境

4. **配置文件错误**
   - 检查JSON格式是否正确
   - 验证店铺信息是否完整
   - 对比原始配置文件

## 📝 使用流程

1. **启动程序**
   ```
   双击 ShopSetup.exe → 等待GUI界面显示
   ```

2. **导入店铺数据**
   ```
   点击"选择文件" → 选择Excel文件 → 确认导入
   ```

3. **开始自动化设置**
   ```
   点击"开始执行" → 程序自动运行 → 查看实时日志
   ```

4. **查看结果**
   ```
   检查日志输出 → 确认设置完成 → 验证店铺配置
   ```

## 📞 技术支持

如果在使用过程中遇到问题：

1. 📋 保存完整的错误日志
2. 📸 截图错误界面
3. 📝 描述具体操作步骤
4. 💬 联系技术支持团队

---

**更新日期：** 2024-12-01  
**程序版本：** ShopSetup v1.0  
**打包工具：** PyInstaller 6.14.1 