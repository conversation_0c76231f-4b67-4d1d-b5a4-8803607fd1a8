#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试WebDriver版本不匹配修复功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from script.shopSetup import ShopSetup

def test_version_mismatch_scenario():
    """测试版本不匹配的场景"""
    print("=" * 60)
    print("测试WebDriver版本不匹配修复功能")
    print("=" * 60)
    
    # 模拟原始错误场景：紫鸟返回Chrome 131，但系统实际是138
    shop_setup = ShopSetup()
    
    # 模拟紫鸟返回的数据（错误的版本信息）
    open_ret_json_wrong = {
        "core_type": "Chromium",
        "core_version": "131.0.6778.76",  # 错误的版本
        "debuggingPort": 48755
    }
    
    # 模拟紫鸟返回的数据（无版本信息）
    open_ret_json_empty = {
        "core_type": "Chromium",
        "debuggingPort": 48755
    }
    
    test_cases = [
        {
            "name": "错误版本信息场景 (Chrome 131 vs 实际 138)",
            "data": open_ret_json_wrong
        },
        {
            "name": "无版本信息场景",
            "data": open_ret_json_empty
        }
    ]
    
    for test_case in test_cases:
        print(f"\n测试场景: {test_case['name']}")
        print("-" * 50)
        
        open_ret_json = test_case['data']
        
        # 测试Socket方式的版本检测和匹配逻辑
        print("🔄 测试Socket方式版本检测...")
        
        # 获取Chrome版本信息
        core_version = open_ret_json.get('core_version')
        if not core_version:
            print("⚠️ 未获取到Chrome版本信息，尝试从系统检测...")
            system_version = shop_setup.chrome_driver_manager.get_system_chrome_version()
            if system_version:
                core_version = f"{system_version}.0.0.0"
                print(f"✅ 从系统检测到Chrome版本: {core_version}")
            else:
                print("⚠️ 系统检测也失败，使用默认版本131")
                core_version = '131.0.0.0'
        
        major_version = core_version.split('.')[0]
        print(f"📋 检测到Chrome版本: {core_version}, 主版本号: {major_version}")
        
        # 获取ChromeDriver路径
        import platform
        if platform.system() == 'Windows':
            chrome_driver_path = os.path.join(shop_setup.driver_folder_path, f'chromedriver{major_version}.exe')
        else:
            chrome_driver_path = os.path.join(shop_setup.driver_folder_path, f'chromedriver{major_version}')
        
        print(f"🔍 查找ChromeDriver路径: {chrome_driver_path}")
        
        # 检查ChromeDriver是否存在
        if os.path.exists(chrome_driver_path):
            print(f"✅ 找到匹配的ChromeDriver: {chrome_driver_path}")
        else:
            print(f"❌ 未找到匹配的ChromeDriver，尝试查找替代版本...")
            
            # 查找可用的ChromeDriver
            available_drivers = shop_setup._find_available_chrome_drivers()
            if available_drivers:
                print(f"📋 找到可用的ChromeDriver: {[os.path.basename(d) for d in available_drivers]}")
                
                # 查找最佳匹配
                best_match = shop_setup._find_best_matching_driver(major_version, available_drivers)
                if best_match:
                    match_version = shop_setup._extract_version_from_path(best_match)
                    print(f"✅ 找到最佳匹配: {os.path.basename(best_match)} (版本: {match_version})")
                    
                    # 模拟版本不匹配的情况
                    if match_version != int(major_version):
                        print(f"⚠️ 版本不匹配: 需要{major_version}，找到{match_version}")
                        print("🔄 模拟系统重新检测...")
                        
                        # 获取系统实际版本
                        system_version = shop_setup.chrome_driver_manager.get_system_chrome_version()
                        if system_version:
                            print(f"✅ 系统实际Chrome版本: {system_version}")
                            
                            # 检查是否有匹配的ChromeDriver
                            if platform.system() == 'Windows':
                                system_driver_path = os.path.join(shop_setup.driver_folder_path, f'chromedriver{system_version}.exe')
                            else:
                                system_driver_path = os.path.join(shop_setup.driver_folder_path, f'chromedriver{system_version}')
                            
                            if os.path.exists(system_driver_path):
                                print(f"✅ 找到系统版本匹配的ChromeDriver: {system_driver_path}")
                            else:
                                # 再次查找最佳匹配
                                best_system_match = shop_setup._find_best_matching_driver(system_version, available_drivers)
                                if best_system_match:
                                    system_match_version = shop_setup._extract_version_from_path(best_system_match)
                                    print(f"✅ 找到系统版本的最佳匹配: {os.path.basename(best_system_match)} (版本: {system_match_version})")
                                else:
                                    print("❌ 未找到系统版本的匹配ChromeDriver")
                        else:
                            print("❌ 无法检测系统Chrome版本")
                else:
                    print("❌ 未找到最佳匹配的ChromeDriver")
            else:
                print("❌ 未找到任何可用的ChromeDriver")

def test_error_handling():
    """测试错误处理逻辑"""
    print("\n" + "=" * 60)
    print("测试错误处理逻辑")
    print("=" * 60)
    
    # 模拟ChromeDriver版本不匹配错误
    error_message = """Message: session not created: cannot connect to chrome at 127.0.0.1:48755
from session not created: This version of ChromeDriver only supports Chrome version 134
Current browser version is 131.0.6778.76"""
    
    print("模拟错误信息:")
    print(error_message)
    print()
    
    # 检查错误检测逻辑
    if "This version of ChromeDriver only supports Chrome version" in error_message:
        print("✅ 成功检测到版本不匹配错误")
        
        # 提取版本信息
        import re
        chrome_version_match = re.search(r'Current browser version is (\d+)\.', error_message)
        driver_version_match = re.search(r'only supports Chrome version (\d+)', error_message)
        
        if chrome_version_match and driver_version_match:
            actual_chrome = chrome_version_match.group(1)
            required_driver = driver_version_match.group(1)
            print(f"📋 实际Chrome版本: {actual_chrome}")
            print(f"📋 ChromeDriver要求版本: {required_driver}")
            print(f"✅ 版本不匹配确认: Chrome {actual_chrome} vs ChromeDriver {required_driver}")
        else:
            print("⚠️ 无法从错误信息中提取版本号")
    else:
        print("❌ 未检测到版本不匹配错误")

if __name__ == "__main__":
    try:
        test_version_mismatch_scenario()
        test_error_handling()
        print("\n" + "=" * 60)
        print("✅ 测试完成")
        print("=" * 60)
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
