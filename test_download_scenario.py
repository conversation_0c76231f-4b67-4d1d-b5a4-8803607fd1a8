#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试ChromeDriver下载场景
"""

import sys
import os
import shutil

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from script.shopSetup import ShopSetup

def test_download_scenario():
    """测试需要下载ChromeDriver的场景"""
    print("=" * 60)
    print("测试ChromeDriver下载场景")
    print("=" * 60)
    
    shop_setup = ShopSetup()
    
    # 模拟系统检测到Chrome 137，但没有对应的ChromeDriver
    print("🔍 模拟场景: 系统Chrome版本137，但没有对应的ChromeDriver")
    print("-" * 50)
    
    # 检查Chrome 137的ChromeDriver是否存在
    import platform
    if platform.system() == 'Windows':
        chrome_driver_137_path = os.path.join(shop_setup.driver_folder_path, 'chromedriver137.exe')
    else:
        chrome_driver_137_path = os.path.join(shop_setup.driver_folder_path, 'chromedriver137')
    
    print(f"🔍 检查ChromeDriver 137路径: {chrome_driver_137_path}")
    
    if os.path.exists(chrome_driver_137_path):
        print("✅ ChromeDriver 137已存在")
    else:
        print("❌ ChromeDriver 137不存在")
        
        # 检查版本映射
        version_mapping = shop_setup.chrome_driver_manager.version_mapping
        if "137" in version_mapping:
            mapped_version = version_mapping["137"]
            print(f"✅ 找到版本映射: Chrome 137 -> ChromeDriver {mapped_version}")
            print(f"📥 可以下载ChromeDriver 137")
            
            # 模拟下载过程（不实际下载）
            print("🔄 模拟下载过程...")
            print(f"   - 下载URL: https://storage.googleapis.com/chrome-for-testing-public/{mapped_version}/win32/chromedriver-win32.zip")
            print(f"   - 目标路径: {chrome_driver_137_path}")
            print("   - 解压缩...")
            print("   - 设置执行权限...")
            print("✅ 模拟下载完成")
        else:
            print("❌ 版本映射中没有Chrome 137")
    
    # 测试替代版本逻辑
    print("\n🔍 测试替代版本逻辑")
    print("-" * 30)
    
    available_drivers = shop_setup._find_available_chrome_drivers()
    if available_drivers:
        print(f"📋 找到可用的ChromeDriver: {[os.path.basename(d) for d in available_drivers]}")
        
        # 测试不同目标版本的最佳匹配
        test_versions = ["131", "135", "137", "139"]
        for target_version in test_versions:
            best_match = shop_setup._find_best_matching_driver(target_version, available_drivers)
            if best_match:
                match_version = shop_setup._extract_version_from_path(best_match)
                version_diff = abs(int(target_version) - match_version)
                compatibility = "✅ 兼容" if version_diff <= 3 else "⚠️ 可能不兼容"
                print(f"目标版本 {target_version} -> 最佳匹配: ChromeDriver {match_version} (差异: {version_diff}) {compatibility}")
            else:
                print(f"目标版本 {target_version} -> ❌ 无匹配")
    else:
        print("❌ 未找到任何可用的ChromeDriver")

def test_gui_integration():
    """测试GUI集成场景"""
    print("\n" + "=" * 60)
    print("测试GUI集成场景")
    print("=" * 60)
    
    # 模拟GUI传递的driver_folder_path
    custom_driver_path = "./custom_drivers"
    
    print(f"🔍 模拟GUI传递的driver目录: {custom_driver_path}")
    
    # 创建自定义目录（如果不存在）
    if not os.path.exists(custom_driver_path):
        os.makedirs(custom_driver_path)
        print(f"✅ 创建自定义driver目录: {custom_driver_path}")
    else:
        print(f"✅ 自定义driver目录已存在: {custom_driver_path}")
    
    # 测试使用自定义路径的ChromeDriverManager
    from ziniao_rpa_base import ChromeDriverManager
    custom_manager = ChromeDriverManager(driver_folder_path=custom_driver_path)
    
    print(f"📋 自定义管理器driver路径: {custom_manager.driver_folder_path}")
    
    # 检查自定义目录中的ChromeDriver
    custom_drivers = []
    try:
        if os.path.exists(custom_driver_path):
            for file in os.listdir(custom_driver_path):
                if file.startswith('chromedriver') and (file.endswith('.exe') or '.' not in file):
                    custom_drivers.append(file)
        
        if custom_drivers:
            print(f"📋 自定义目录中的ChromeDriver: {custom_drivers}")
        else:
            print("📋 自定义目录中没有ChromeDriver")
            print("📥 需要下载到自定义目录")
    except Exception as e:
        print(f"❌ 检查自定义目录失败: {str(e)}")
    
    # 清理测试目录
    try:
        if os.path.exists(custom_driver_path) and not custom_drivers:
            os.rmdir(custom_driver_path)
            print(f"🧹 清理空的测试目录: {custom_driver_path}")
    except Exception as e:
        print(f"⚠️ 清理测试目录失败: {str(e)}")

def test_error_recovery():
    """测试错误恢复场景"""
    print("\n" + "=" * 60)
    print("测试错误恢复场景")
    print("=" * 60)
    
    shop_setup = ShopSetup()
    
    # 模拟版本不匹配错误的恢复过程
    print("🔍 模拟版本不匹配错误恢复")
    print("-" * 40)
    
    # 假设紫鸟返回Chrome 131，但系统实际是138
    紫鸟_version = "131"
    系统_version = shop_setup.chrome_driver_manager.get_system_chrome_version()
    
    print(f"📋 紫鸟返回版本: {紫鸟_version}")
    print(f"📋 系统实际版本: {系统_version}")
    
    if 紫鸟_version != 系统_version:
        print("⚠️ 版本不一致，触发错误恢复机制")
        
        # 检查系统版本的ChromeDriver是否存在
        import platform
        if platform.system() == 'Windows':
            system_driver_path = os.path.join(shop_setup.driver_folder_path, f'chromedriver{系统_version}.exe')
        else:
            system_driver_path = os.path.join(shop_setup.driver_folder_path, f'chromedriver{系统_version}')
        
        if os.path.exists(system_driver_path):
            print(f"✅ 找到系统版本的ChromeDriver: {system_driver_path}")
            print("✅ 错误恢复成功：使用系统版本的ChromeDriver")
        else:
            print(f"❌ 系统版本的ChromeDriver不存在: {system_driver_path}")
            
            # 查找最佳替代版本
            available_drivers = shop_setup._find_available_chrome_drivers()
            if available_drivers:
                best_match = shop_setup._find_best_matching_driver(系统_version, available_drivers)
                if best_match:
                    match_version = shop_setup._extract_version_from_path(best_match)
                    print(f"✅ 找到最佳替代版本: ChromeDriver {match_version}")
                    print("✅ 错误恢复成功：使用最佳替代版本")
                else:
                    print("❌ 错误恢复失败：无法找到合适的替代版本")
            else:
                print("❌ 错误恢复失败：没有任何可用的ChromeDriver")
    else:
        print("✅ 版本一致，无需错误恢复")

if __name__ == "__main__":
    try:
        test_download_scenario()
        test_gui_integration()
        test_error_recovery()
        print("\n" + "=" * 60)
        print("✅ 所有下载场景测试完成")
        print("=" * 60)
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
