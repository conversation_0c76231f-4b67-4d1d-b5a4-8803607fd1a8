"""
可配置的GUI框架

支持通过配置动态构建界面，不需要修改代码就能添加新组件
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from tkinter import scrolledtext
from typing import Dict, List, Any, Callable, Optional
import json
import os
import logging
import threading
from datetime import datetime
from abc import ABC, abstractmethod
from global_logger import set_log_widget, log_info, log_warning, log_error, log_success, log_step, log_task_start, log_task_end, log_separator


class ComponentConfig:
    """组件配置类"""
    
    def __init__(self, component_type: str, label: str, order: float = None, **kwargs):
        self.component_type = component_type  # 组件类型
        self.label = label  # 显示标签
        self.order = order  # 组件显示顺序，数值越小越靠前，None表示使用默认排序
        self.config = kwargs  # 其他配置参数
        
    def get(self, key: str, default=None):
        """获取配置参数"""
        return self.config.get(key, default)


class VariableManager:
    """变量管理器"""
    
    def __init__(self):
        self.variables = {}
    
    def register_variable(self, name: str, variable):
        """注册变量"""
        self.variables[name] = variable
    
    def get_variable(self, name: str):
        """获取变量"""
        return self.variables.get(name)


class EventManager:
    """事件管理器"""
    
    def __init__(self, gui_instance):
        self.gui = gui_instance
        self.commands = {}
        self.button_states = {}  # 记录按钮状态
        self.async_tasks = {}    # 记录异步任务
        # 这些命令已经有自己的异步处理逻辑，不需要再包装
        self.sync_commands = {'start_program', 'refresh_stores'}
    
    def register_command(self, name: str, command: Callable):
        """注册命令"""
        self.commands[name] = command
    
    def get_command(self, name: str):
        """获取命令 - 根据需要返回异步包装的命令"""
        original_command = self.commands.get(name)
        if original_command:
            # 如果是同步命令列表中的命令，直接返回原命令
            if name in self.sync_commands:
                return original_command
            # 否则返回异步包装的命令
            return self._create_async_wrapper(name, original_command)
        return None
    
    def _create_async_wrapper(self, command_name: str, original_command: Callable):
        """创建异步命令包装器"""
        def async_wrapper(*args, **kwargs):
            # 检查是否已有同名任务在执行
            if command_name in self.async_tasks and self.async_tasks[command_name].is_alive():
                self.gui.log_warning(f"命令 '{command_name}' 正在执行中，请稍候...")
                return
            
            # 记录任务开始
            self.gui.log_info(f"开始执行命令: {command_name}")
            
            # 禁用相关按钮
            self._disable_command_button(command_name)
            
            # 创建并启动异步任务
            import threading
            thread = threading.Thread(
                target=self._run_command_threaded,
                args=(command_name, original_command, args, kwargs)
            )
            thread.daemon = True
            self.async_tasks[command_name] = thread
            thread.start()
        
        return async_wrapper
    
    def _run_command_threaded(self, command_name: str, command: Callable, args, kwargs):
        """在线程中执行命令"""
        try:
            # 执行原始命令
            result = command(*args, **kwargs)
            
            # 在主线程中处理完成状态
            self.gui.window.after_idle(
                lambda: self._handle_command_success(command_name, result)
            )
            
        except Exception as e:
            # 在主线程中处理错误状态
            self.gui.window.after_idle(
                lambda: self._handle_command_error(command_name, e)
            )
    
    def _handle_command_success(self, command_name: str, result):
        """处理命令执行成功"""
        self.gui.log_info(f"✅ 命令 '{command_name}' 执行完成")
        self._enable_command_button(command_name)
        
        # 清理任务记录
        if command_name in self.async_tasks:
            del self.async_tasks[command_name]
    
    def _handle_command_error(self, command_name: str, error):
        """处理命令执行错误"""
        self.gui.log_error(f"❌ 命令 '{command_name}' 执行失败: {str(error)}")
        self._enable_command_button(command_name)
        
        # 清理任务记录
        if command_name in self.async_tasks:
            del self.async_tasks[command_name]
    
    def _disable_command_button(self, command_name: str):
        """禁用命令对应的按钮"""
        button = self._find_button_by_command(command_name)
        if button:
            original_text = button.cget("text")
            self.button_states[command_name] = {
                'button': button,
                'original_text': original_text,
                'original_state': button.cget("state")
            }
            button.configure(state="disabled", text=f"{original_text} (执行中...)")
    
    def _enable_command_button(self, command_name: str):
        """启用命令对应的按钮"""
        if command_name in self.button_states:
            button_info = self.button_states[command_name]
            button = button_info['button']
            try:
                button.configure(
                    state=button_info['original_state'],
                    text=button_info['original_text']
                )
            except:
                pass  # 按钮可能已被销毁
            del self.button_states[command_name]
    
    def _find_button_by_command(self, command_name: str):
        """根据命令名查找对应的按钮"""
        def find_in_widget(widget):
            if isinstance(widget, ttk.Button):
                try:
                    # 检查按钮的命令是否匹配
                    button_command = widget.cget("command")
                    if button_command and command_name in str(button_command):
                        return widget
                except:
                    pass
            
            # 递归查找子组件
            try:
                for child in widget.winfo_children():
                    result = find_in_widget(child)
                    if result:
                        return result
            except:
                pass
            return None
        
        if hasattr(self.gui, 'scrollable_frame'):
            return find_in_widget(self.gui.scrollable_frame)
        return None
    
    def add_sync_command(self, command_name: str):
        """添加同步命令（不进行异步包装）"""
        self.sync_commands.add(command_name)
    
    def remove_sync_command(self, command_name: str):
        """移除同步命令配置"""
        self.sync_commands.discard(command_name)
    
    def get_auto_save_callback(self):
        """获取自动保存回调"""
        return lambda *args: self.gui.auto_save_config()


class ComponentFactory:
    """组件工厂类"""
    
    @staticmethod
    def create_component(parent, config: ComponentConfig, row: int, variable_manager, event_manager):
        """创建组件"""
        component_type = config.component_type
        
        if component_type == "entry":
            return ComponentFactory._create_entry(parent, config, row, variable_manager, event_manager)
        elif component_type == "file_selector":
            return ComponentFactory._create_file_selector(parent, config, row, variable_manager, event_manager)
        elif component_type == "folder_selector":
            return ComponentFactory._create_folder_selector(parent, config, row, variable_manager, event_manager)
        elif component_type == "password":
            return ComponentFactory._create_password(parent, config, row, variable_manager, event_manager)
        elif component_type == "checkbox_group":
            return ComponentFactory._create_checkbox_group(parent, config, row, variable_manager, event_manager)
        elif component_type == "checkbox":
            return ComponentFactory._create_checkbox(parent, config, row, variable_manager, event_manager)
        elif component_type == "scrollable_checkbox":
            return ComponentFactory._create_scrollable_checkbox(parent, config, row, variable_manager, event_manager)
        elif component_type == "button_group":
            return ComponentFactory._create_button_group(parent, config, row, variable_manager, event_manager)
        elif component_type == "label":
            return ComponentFactory._create_label(parent, config, row, variable_manager, event_manager)
        elif component_type == "log_panel":
            return ComponentFactory._create_log_panel(parent, config, row, variable_manager, event_manager)
        elif component_type == "frame_group":
            return ComponentFactory._create_frame_group(parent, config, row, variable_manager, event_manager)
        else:
            raise ValueError(f"不支持的组件类型: {component_type}")
    
    @staticmethod
    def _create_entry(parent, config, row, variable_manager, event_manager):
        """创建文本输入框"""
        ttk.Label(parent, text=config.label).grid(row=row, column=0, sticky=tk.W)
        
        var_name = config.get('variable', f'var_{row}')
        var = tk.StringVar()
        variable_manager.register_variable(var_name, var)
        
        # 绑定自动保存事件
        if config.get('auto_save', True):
            var.trace_add("write", event_manager.get_auto_save_callback())
        
        entry = ttk.Entry(parent, textvariable=var, width=config.get('width', 50))
        entry.grid(row=row, column=1, padx=5)
        
        return entry
    
    @staticmethod
    def _create_file_selector(parent, config, row, variable_manager, event_manager):
        """创建文件选择器"""
        ttk.Label(parent, text=config.label).grid(row=row, column=0, sticky=tk.W)
        
        var_name = config.get('variable', f'var_{row}')
        var = tk.StringVar()
        variable_manager.register_variable(var_name, var)
        
        if config.get('auto_save', True):
            var.trace_add("write", event_manager.get_auto_save_callback())
        
        entry = ttk.Entry(parent, textvariable=var, width=config.get('width', 50))
        entry.grid(row=row, column=1, padx=5)
        
        def select_file():
            filetypes = config.get('filetypes', [("所有文件", "*.*")])
            path = filedialog.askopenfilename(title=config.get('title', '选择文件'), filetypes=filetypes)
            if path:
                var.set(path)
        
        button = ttk.Button(parent, text="浏览", command=select_file)
        button.grid(row=row, column=2)
        
        return (entry, button)
    
    @staticmethod
    def _create_folder_selector(parent, config, row, variable_manager, event_manager):
        """创建文件夹选择器"""
        ttk.Label(parent, text=config.label).grid(row=row, column=0, sticky=tk.W)
        
        var_name = config.get('variable', f'var_{row}')
        var = tk.StringVar()
        variable_manager.register_variable(var_name, var)
        
        if config.get('auto_save', True):
            var.trace_add("write", event_manager.get_auto_save_callback())
        
        entry = ttk.Entry(parent, textvariable=var, width=config.get('width', 50))
        entry.grid(row=row, column=1, padx=5)
        
        def select_folder():
            path = filedialog.askdirectory(title=config.get('title', '选择文件夹'))
            if path:
                var.set(path)
        
        button = ttk.Button(parent, text="浏览", command=select_folder)
        button.grid(row=row, column=2)
        
        return (entry, button)
    
    @staticmethod
    def _create_password(parent, config, row, variable_manager, event_manager):
        """创建密码输入框"""
        ttk.Label(parent, text=config.label).grid(row=row, column=0, sticky=tk.W)
        
        var_name = config.get('variable', f'var_{row}')
        var = tk.StringVar()
        variable_manager.register_variable(var_name, var)
        
        if config.get('auto_save', True):
            var.trace_add("write", event_manager.get_auto_save_callback())
        
        entry = ttk.Entry(parent, textvariable=var, width=config.get('width', 50), show="*")
        entry.grid(row=row, column=1, padx=5)
        
        return entry
    
    @staticmethod
    def _create_checkbox_group(parent, config, row, variable_manager, event_manager):
        """创建复选框组"""
        ttk.Label(parent, text=config.label).grid(row=row, column=0, sticky=tk.W)
        
        frame = ttk.Frame(parent)
        frame.grid(row=row, column=1, columnspan=2, sticky=tk.W)
        
        options = config.get('options', [])
        columns = config.get('columns', 3)
        
        vars_dict = {}
        for i, option in enumerate(options):
            var = tk.BooleanVar()
            vars_dict[option] = var
            
            checkbox = ttk.Checkbutton(frame, text=option, variable=var)
            checkbox.grid(row=i//columns, column=i%columns, sticky=tk.W, padx=5, pady=2)
        
        var_name = config.get('variable', f'checkbox_group_{row}')
        variable_manager.register_variable(var_name, vars_dict)
        
        return frame
    
    @staticmethod
    def _create_checkbox(parent, config, row, variable_manager, event_manager):
        """创建单个复选框"""
        var_name = config.get('variable', f'checkbox_{row}')
        var = tk.BooleanVar()
        
        # 设置默认值
        default_value = config.get('default', False)
        var.set(default_value)
        
        variable_manager.register_variable(var_name, var)
        
        # 绑定自动保存事件
        if config.get('auto_save', True):
            var.trace_add("write", event_manager.get_auto_save_callback())
        
        checkbox = ttk.Checkbutton(parent, text=config.label, variable=var)
        checkbox.grid(row=row, column=0, columnspan=3, sticky=tk.W, pady=2)
        
        # 如果是子模块，记录组件引用并隐藏
        parent_module = config.get('parent_module')
        if parent_module:
            print(f"[DEBUG] 检测到子模块: {var_name}, 父模块: {parent_module}")
            
            # 获取GUI实例
            gui_instance = getattr(event_manager, 'gui_instance', None)
            if gui_instance and hasattr(gui_instance, 'rpa'):
                print(f"[DEBUG] GUI实例和RPA实例都存在")
                
                # 确保RPA实例有submodule_widgets属性
                if not hasattr(gui_instance.rpa, 'submodule_widgets'):
                    gui_instance.rpa.submodule_widgets = {}
                    print(f"[DEBUG] 初始化submodule_widgets")
                
                if parent_module not in gui_instance.rpa.submodule_widgets:
                    gui_instance.rpa.submodule_widgets[parent_module] = []
                    print(f"[DEBUG] 为父模块{parent_module}初始化子模块列表")
                
                gui_instance.rpa.submodule_widgets[parent_module].append({
                    'widget': checkbox,
                    'variable': var_name
                })
                
                print(f"[DEBUG] 子模块{var_name}已记录到{parent_module}中")
                print(f"[DEBUG] 当前{parent_module}下有{len(gui_instance.rpa.submodule_widgets[parent_module])}个子模块")
                
                # 子模块默认隐藏（使用grid_remove移除但保持配置）
                checkbox.grid_remove()
                print(f"[DEBUG] 子模块{var_name}已隐藏")
            else:
                print(f"[DEBUG] GUI实例或RPA实例不存在，无法记录子模块")
        
        return checkbox
    
    @staticmethod
    def _create_scrollable_checkbox(parent, config, row, variable_manager, event_manager):
        """创建带滚动条和筛选功能的复选框组"""
        ttk.Label(parent, text=config.label).grid(row=row, column=0, sticky=tk.W)
        
        # 创建主容器
        main_container = ttk.Frame(parent)
        main_container.grid(row=row, column=1, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 添加筛选功能（如果是店铺选择组件）
        var_name = config.get('variable', f'scrollable_checkbox_{row}')
        if var_name == 'stores':
            # 创建筛选输入框
            filter_frame = ttk.Frame(main_container)
            filter_frame.pack(fill=tk.X, pady=(0, 5))
            
            ttk.Label(filter_frame, text="🔍 筛选店铺:").pack(side=tk.LEFT, padx=(0, 5))
            
            filter_var = tk.StringVar()
            filter_entry = ttk.Entry(filter_frame, textvariable=filter_var, width=30)
            filter_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
            
            # 操作按钮组
            button_frame = ttk.Frame(filter_frame)
            button_frame.pack(side=tk.RIGHT)
            
            # 全选按钮
            def select_all_stores():
                stores_vars = variable_manager.get_variable('stores')
                if stores_vars:
                    for var in stores_vars.values():
                        var.set(True)
            
            select_all_button = ttk.Button(button_frame, text="全选", command=select_all_stores, width=6)
            select_all_button.pack(side=tk.LEFT, padx=(0, 2))
            
            # 取消全选按钮
            def deselect_all_stores():
                stores_vars = variable_manager.get_variable('stores')
                if stores_vars:
                    for var in stores_vars.values():
                        var.set(False)
            
            deselect_all_button = ttk.Button(button_frame, text="取消全选", command=deselect_all_stores, width=8)
            deselect_all_button.pack(side=tk.LEFT, padx=(0, 2))
            
            # 清空筛选按钮
            def clear_filter():
                filter_var.set("")
            
            clear_button = ttk.Button(button_frame, text="清空", command=clear_filter, width=6)
            clear_button.pack(side=tk.LEFT)
            
            # 注册筛选变量
            variable_manager.register_variable(f'{var_name}_filter', filter_var)
            variable_manager.register_variable(f'{var_name}_filter_entry', filter_entry)
        
        # 创建滚动容器
        scroll_container = ttk.Frame(main_container)
        scroll_container.pack(fill=tk.BOTH, expand=True)
        
        # 创建Canvas和Scrollbar
        canvas = tk.Canvas(scroll_container, height=config.get('height', 150))
        scrollbar = ttk.Scrollbar(scroll_container, orient="vertical", command=canvas.yview)
        
        # 创建内容框架
        content_frame = ttk.Frame(canvas)
        
        # 配置Canvas
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 放置Canvas和Scrollbar
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 创建窗口
        canvas_frame = canvas.create_window((0, 0), window=content_frame, anchor="nw")
        
        # 绑定事件
        def configure_scroll_region(event):
            canvas.configure(scrollregion=canvas.bbox("all"))
        
        def configure_canvas_width(event):
            canvas.itemconfig(canvas_frame, width=event.width)
        
        content_frame.bind("<Configure>", configure_scroll_region)
        canvas.bind("<Configure>", configure_canvas_width)
        
        # 存储变量和框架
        variables = {}
        variable_manager.register_variable(var_name, variables)
        variable_manager.register_variable(f'{var_name}_frame', content_frame)
        variable_manager.register_variable(f'{var_name}_canvas', canvas)
        variable_manager.register_variable(f'{var_name}_container', main_container)
        
        return main_container
    
    @staticmethod
    def _create_button_group(parent, config, row, variable_manager, event_manager):
        """创建按钮组"""
        button_frame = ttk.Frame(parent)
        button_frame.grid(row=row, column=1, columnspan=2, pady=10)
        
        buttons = config.get('buttons', [])
        for i, button_config in enumerate(buttons):
            text = button_config.get('text', f'按钮{i}')
            command_name = button_config.get('command')
            command = event_manager.get_command(command_name) if command_name else None
            
            button = ttk.Button(button_frame, text=text, command=command)
            button.pack(side=tk.LEFT, padx=5)
        
        return button_frame
    
    @staticmethod
    def _create_label(parent, config, row, variable_manager, event_manager):
        """创建标签"""
        # 检查是否需要特殊样式
        font = config.get('font', None)
        fg = config.get('fg', None)
        
        if font or fg:
            # 使用tk.Label以支持字体和颜色
            label = tk.Label(parent, text=config.label)
            if font:
                label.configure(font=font)
            if fg:
                label.configure(fg=fg)
                # 获取父组件背景色
                try:
                    bg_color = parent.cget('bg')
                except:
                    bg_color = 'SystemButtonFace'
                label.configure(bg=bg_color)
        else:
            # 使用默认的ttk.Label
            label = ttk.Label(parent, text=config.label)
        
        label.grid(row=row, column=0, columnspan=3, sticky=tk.W, pady=config.get('pady', 5))
        
        # 如果有变量名，注册到变量管理器
        var_name = config.get('variable')
        if var_name:
            variable_manager.register_variable(var_name, label)
        
        return label
    
    @staticmethod
    def _create_log_panel(parent, config, row, variable_manager, event_manager):
        """创建日志面板"""
        ttk.Label(parent, text=config.label).grid(row=row, column=0, sticky=tk.W)
        
        # 创建日志面板容器
        log_container = ttk.Frame(parent)
        log_container.grid(row=row, column=1, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        
        # 创建文本框和滚动条
        text_frame = ttk.Frame(log_container)
        text_frame.pack(fill=tk.BOTH, expand=True)
        
        # 文本框
        log_text = tk.Text(text_frame, 
                          height=config.get('height', 15),
                          width=config.get('width', 80),
                          wrap=tk.WORD,
                          font=('Consolas', 9),
                          bg='#f8f8f8',
                          fg='#333333')
        
        # 垂直滚动条
        v_scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=log_text.yview)
        log_text.configure(yscrollcommand=v_scrollbar.set)
        
        # 水平滚动条
        h_scrollbar = ttk.Scrollbar(text_frame, orient=tk.HORIZONTAL, command=log_text.xview)
        log_text.configure(xscrollcommand=h_scrollbar.set)
        
        # 布局
        log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        # 配置权重
        text_frame.grid_rowconfigure(0, weight=1)
        text_frame.grid_columnconfigure(0, weight=1)
        
        # 控制按钮
        button_frame = ttk.Frame(log_container)
        button_frame.pack(fill=tk.X, pady=(5, 0))
        
        def clear_log():
            log_text.delete(1.0, tk.END)
        
        def save_log():
            from tkinter import filedialog
            filename = filedialog.asksaveasfilename(
                title="保存日志",
                filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")],
                defaultextension=".txt"
            )
            if filename:
                try:
                    with open(filename, 'w', encoding='utf-8') as f:
                        f.write(log_text.get(1.0, tk.END))
                    messagebox.showinfo("成功", f"日志已保存到: {filename}")
                except Exception as e:
                    messagebox.showerror("错误", f"保存日志失败: {str(e)}")
        
        ttk.Button(button_frame, text="清空日志", command=clear_log).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="保存日志", command=save_log).pack(side=tk.LEFT, padx=5)
        
        # 注册到变量管理器
        var_name = config.get('variable', 'log_panel')
        variable_manager.register_variable(var_name, log_text)
        
        return log_container
    
    @staticmethod
    def _create_frame_group(parent, config, row, variable_manager, event_manager):
        """创建带边框的分组框架"""
        # 创建LabelFrame
        frame = ttk.LabelFrame(parent, text=config.label, padding=10)
        frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5, padx=5)
        
        # 配置内部网格权重
        frame.grid_columnconfigure(1, weight=1)
        
        # 获取子组件配置
        sub_components = config.get('components', [])
        
        # 创建子组件
        for i, sub_config in enumerate(sub_components):
            ComponentFactory.create_component(
                frame, sub_config, i, variable_manager, event_manager
            )
        
        return frame


class GUILogHandler(logging.Handler):
    """GUI日志处理器，将日志输出到GUI面板"""
    
    def __init__(self, log_widget=None):
        super().__init__()
        self.log_widget = log_widget
        self.formatter = logging.Formatter(
            '%(asctime)s [%(levelname)s] %(message)s',
            datefmt='%H:%M:%S'
        )
    
    def set_log_widget(self, log_widget):
        """设置日志显示组件"""
        self.log_widget = log_widget
    
    def emit(self, record):
        """输出日志记录"""
        if self.log_widget is None:
            return
        
        try:
            # 格式化日志消息
            msg = self.format(record)
            
            # 在主线程中更新GUI
            def update_log():
                try:
                    self.log_widget.insert(tk.END, msg + '\n')
                    # 自动滚动到底部
                    self.log_widget.see(tk.END)
                    # 限制日志行数，避免内存占用过大
                    lines = self.log_widget.get(1.0, tk.END).count('\n')
                    if lines > 1000:  # 超过1000行删除前面的
                        self.log_widget.delete(1.0, f"{lines-800}.0")
                except:
                    pass  # 忽略GUI更新错误
            
            # 如果在主线程中则直接更新，否则安排到主线程
            if threading.current_thread() == threading.main_thread():
                update_log()
            else:
                self.log_widget.after_idle(update_log)
                
        except Exception:
            pass  # 忽略日志处理错误，避免影响程序运行


class ConfigurableGUI:
    """可配置的GUI基类"""
    
    def __init__(self, rpa_instance, components_config: List[ComponentConfig] = None):
        """
        初始化可配置GUI
        
        Args:
            rpa_instance: BaseZiniaoRPA的实例，用于执行具体的RPA任务
            components_config: 可选的自定义组件配置列表
        """
        self.rpa = rpa_instance
        self.components_config = components_config or []
        
        # 首先初始化日志处理器，确保在其他操作中可以使用日志
        self.log_handler = GUILogHandler()
        self.logger = logging.getLogger('GUI')
        self.logger.setLevel(logging.INFO)
        self.logger.addHandler(self.log_handler)
        
        # 添加默认组件
        default_components = self._get_default_components()
        for component in default_components:
            if component not in self.components_config:
                self.components_config.append(component)
        
        # 按order排序组件
        self.components_config.sort(key=lambda x: x.order if x.order is not None else float('inf'))
        
        # 初始化管理器
        self.variable_manager = VariableManager()
        # 初始化事件管理器
        self.event_manager = EventManager(self)
        self.event_manager.gui_instance = self  # 设置GUI实例引用
        
        # 任务控制相关属性
        self.task_control = {
            'is_running': False,
            'is_paused': False,
            'pause_event': threading.Event(),
            'stop_event': threading.Event(),
            'current_thread': None
        }
        
        # 初始化暂停事件为已设置状态（非暂停）
        self.task_control['pause_event'].set()
        
        # 在构建GUI前进行预设置
        self._pre_gui_setup()
        
        # 注册默认命令
        self._register_default_commands()
        
        # 构建GUI
        self._build_gui()
        
        # 设置日志
        self._setup_logging()
        
        # 加载配置
        self.load_config()
        
        self.log_info(f"已启动 {self.rpa.get_task_name()} GUI界面")
        
        # 设置初始任务控制按钮状态
        self._update_task_control_buttons("idle")
    
    def _pre_gui_setup(self):
        """
        在GUI构建之前进行的设置
        子类可以重写此方法来注册自定义命令和添加组件
        """
        pass
    
    def _get_default_components(self):
        """
        获取默认组件配置 - 包含所有RPA任务的标准组件
        
        继承ConfigurableGUI的类会自动获得以下标准功能：
        - 紫鸟浏览器路径配置
        - WebDriver路径配置  
        - 用户认证信息（公司名称、用户名、密码）
        - 上传文件管理（可选，取决于RPA实例配置）
        - 国家和店铺选择
        - 标准操作按钮（刷新店铺列表、开始任务）
        - 实时日志面板
        
        子类可以通过以下方式扩展：
        1. 重写此方法添加自定义组件
        2. 使用add_component()动态添加组件
        3. 使用add_custom_command()添加自定义按钮命令
        """
        components = [
            # ========== 基本配置组 (order: 10-29) ==========
            ComponentConfig("label", "=== 基本配置 ===", 
                          order=10, pady=10),
            
            ComponentConfig("file_selector", "紫鸟浏览器路径:", 
                          order=11,
                          variable="client_path", 
                          filetypes=[("可执行文件", "*.exe"), ("所有文件", "*.*")],
                          title="选择紫鸟浏览器启动程序"),
            
            ComponentConfig("folder_selector", "WebDriver路径:", 
                          order=12,
                          variable="driver_path", 
                          title="选择WebDriver目录"),
            
            # ========== 用户认证组 (order: 30-49) ==========
            ComponentConfig("label", "=== 用户认证 ===", 
                          order=30, pady=10),
            
            ComponentConfig("entry", "公司名称:", 
                          order=31,
                          variable="company",
                          width=50),
            
            ComponentConfig("entry", "用户名:", 
                          order=32,
                          variable="username",
                          width=50),
            
            ComponentConfig("password", "密码:", 
                          order=33,
                          variable="password",
                          width=50),
        ]
        
        # ========== 文件管理组 (order: 50-69) - 根据RPA实例决定是否显示 ==========
        if hasattr(self.rpa, 'requires_upload_folder') and self.rpa.requires_upload_folder():
            components.extend([
                ComponentConfig("label", "=== 文件管理 ===", 
                              order=50, pady=10),
                
                ComponentConfig("folder_selector", "上传文件文件夹:", 
                              order=51,
                              variable="upload_folder", 
                              title="选择上传文件文件夹"),
            ])
        
        # ========== 国家和店铺选择组 (order: 70-89) ==========
        components.extend([
            ComponentConfig("frame_group", "🌍 国家选择", 
                          order=70,
                          components=[
                              ComponentConfig("checkbox_group", "选择目标国家:", 
                                            variable="countries",
                                            options=[
                                                "阿拉伯联合酋长国", "埃及", "爱尔兰", "澳大利亚", 
                                                "比利时", "波兰", "德国", "法国", "荷兰", 
                                                "加拿大", "美国", "墨西哥", "日本", "瑞典", 
                                                "沙特阿拉伯", "西班牙", "新加坡", "意大利", "英国"
                                            ],
                                            columns=3)
                          ]),
            
            ComponentConfig("frame_group", "🏪 店铺选择", 
                          order=80,
                          components=[
                              ComponentConfig("scrollable_checkbox", "选择目标店铺:", 
                                            variable="stores",
                                            height=150),
                              ComponentConfig("button_group", "", 
                                            order=81,
                                            buttons=[
                                                {"text": "刷新店铺列表", "command": "refresh_stores"}
                                            ])
                          ]),
            
            # ========== 帮助和说明组 (order: 85-89) ==========
            ComponentConfig("frame_group", "📖 帮助和说明",
                          order=85,
                          components=[
                              ComponentConfig("button_group", "",
                                            order=1,
                                            buttons=[
                                                {"text": "📖 使用说明书", "command": "show_user_manual", "width": 15},
                                                {"text": "❓ 快速帮助", "command": "show_quick_help", "width": 15}
                                            ])
                          ]),

            # ========== 标准操作组 (order: 90-99) ==========
            ComponentConfig("label", "=== 标准操作 ===",
                          order=90, pady=10),
            
            ComponentConfig("button_group", "", 
                          order=91,
                          buttons=[
                              {"text": "开始任务", "command": "start_program"}
                          ]),
            
            ComponentConfig("button_group", "", 
                          order=92,
                          buttons=[
                              {"text": "⏸️ 暂停任务", "command": "pause_task"},
                              {"text": "▶️ 继续任务", "command": "resume_task"},
                              {"text": "⏹️ 结束任务", "command": "stop_task"}
                          ]),
            
            ComponentConfig("label", "任务状态: 就绪", 
                          order=93, 
                          variable="task_status",
                          font=("Arial", 10, "bold"),
                          fg="green"),
            
            # ========== 实时日志面板（永远在最底部，order: 1000） ==========
            ComponentConfig("label", "=== 实时日志 ===", 
                          order=1000, pady=10),
            
            ComponentConfig("log_panel", "任务日志:", 
                          order=1001,
                          variable="log_panel",
                          height=15,
                          width=100)
        ])
        
        return components
    
    def _register_default_commands(self):
        """注册默认命令"""
        self.event_manager.register_command("refresh_stores", self.refresh_store_list)
        # 设置刷新店铺为同步命令，避免按钮自动禁用（我们手动控制）
        self.event_manager.add_sync_command("refresh_stores")
        self.event_manager.register_command("start_program", self.start_program)
        self.event_manager.register_command("pause_task", self.pause_task)
        self.event_manager.register_command("resume_task", self.resume_task)
        self.event_manager.register_command("stop_task", self.stop_task)
        # 添加帮助和说明命令
        self.event_manager.register_command("show_user_manual", self.show_user_manual)
        self.event_manager.register_command("show_quick_help", self.show_quick_help)
    
    def _get_next_available_order(self):
        """
        获取下一个可用的排序值
        
        返回当前最大排序值+10（排除日志组件的1000+）
        如果没有组件或都没有设置order，返回100
        """
        max_order = 0
        for component in self.components_config:
            if component.order is not None and component.order < 1000:  # 排除日志组件
                max_order = max(max_order, component.order)
        
        return max_order + 10 if max_order > 0 else 100
    
    def add_component(self, config: ComponentConfig):
        """
        动态添加组件
        
        如果组件没有设置order，会自动分配一个order值（当前最大值+10，但小于1000）
        """
        if config.order is None:
            config.order = self._get_next_available_order()
        
        self.components_config.append(config)
        
        # 只在不是重建过程中时输出日志和重建
        if not getattr(self, '_rebuilding', False):
            self.log_info(f"添加组件: {config.label} (order: {config.order})")
            
            # 如果GUI已经构建，需要重新构建以显示新组件
            if hasattr(self, 'scrollable_frame'):
                self._rebuild_gui()
    
    def add_components(self, configs: List[ComponentConfig]):
        """批量添加组件"""
        for config in configs:
            self.add_component(config)
    
    def insert_component(self, index: int, config: ComponentConfig):
        """
        在指定位置插入组件
        
        注意：使用order排序后，index位置可能会改变，建议使用order属性控制位置
        """
        if config.order is None:
            config.order = self._get_next_available_order()
        
        self.components_config.insert(index, config)
        self.log_info(f"插入组件: {config.label} (order: {config.order})")
    
    def add_section(self, section_name: str, components: List[ComponentConfig], base_order: float = None):
        """
        添加一个完整的功能区块
        
        Args:
            section_name: 区块名称，如 "=== 高级设置 ==="
            components: 该区块包含的组件列表
            base_order: 基础排序值，如果不提供则自动分配
        """
        if base_order is None:
            base_order = self._get_next_available_order()
        
        # 添加区块标题
        section_config = ComponentConfig("label", section_name, order=base_order, pady=10)
        self.add_component(section_config)
        
        # 添加区块内的组件，按0.1递增
        for i, component in enumerate(components):
            if component.order is None:
                component.order = base_order + 0.1 + (i * 0.1)
            self.add_component(component)
        
        # 只在不是重建过程中时输出日志和重建
        if not getattr(self, '_rebuilding', False):
            self.log_info(f"添加功能区块: {section_name} (包含 {len(components)} 个组件)")
            
            # 如果GUI已经构建，需要重新构建以显示新组件
            if hasattr(self, 'scrollable_frame'):
                self._rebuild_gui()
    
    def add_button_group(self, buttons: List[dict], label: str = "", order: float = None):
        """
        添加按钮组的便利方法
        
        Args:
            buttons: 按钮配置列表，格式: [{"text": "按钮名", "command": "命令名"}]
            label: 可选的标签文本
            order: 排序值，如果不提供则自动分配
        """
        if order is None:
            order = self._get_next_available_order()
        
        if label:
            self.add_component(ComponentConfig("label", label, order=order, pady=5))
            order += 0.1
        
        self.add_component(ComponentConfig("button_group", "", order=order, buttons=buttons))
    
    def add_custom_command(self, name: str, command: Callable):
        """添加自定义命令"""
        self.event_manager.register_command(name, command)
    
    def add_custom_commands(self, commands: dict):
        """
        批量添加自定义命令
        
        Args:
            commands: 命令字典，格式: {"命令名": 命令函数}
        """
        for name, command in commands.items():
            self.add_custom_command(name, command)
    
    def set_command_sync(self, command_name: str, is_sync: bool = True):
        """
        设置命令是否同步执行
        
        Args:
            command_name: 命令名称
            is_sync: True为同步执行，False为异步执行（默认）
        """
        if is_sync:
            self.event_manager.add_sync_command(command_name)
        else:
            self.event_manager.remove_sync_command(command_name)
    
    def _build_gui(self):
        """构建GUI界面（支持滚动）"""
        # 初始化窗口
        self.window = tk.Tk()
        task_name = self.rpa.get_task_name()
        self.window.title(f"紫鸟浏览器配置 - {task_name}")
        self.window.geometry("1000x800")  # 增大默认窗口尺寸
        
        # 设置样式
        style = ttk.Style()
        style.configure("TLabel", padding=5)
        style.configure("TEntry", padding=5)
        style.configure("TButton", padding=5)
        
        # 创建主容器
        main_container = ttk.Frame(self.window)
        main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建Canvas和滚动条用于滚动功能
        canvas = tk.Canvas(main_container, highlightthickness=0)
        v_scrollbar = ttk.Scrollbar(main_container, orient=tk.VERTICAL, command=canvas.yview)
        h_scrollbar = ttk.Scrollbar(main_container, orient=tk.HORIZONTAL, command=canvas.xview)
        
        # 创建可滚动的内容框架
        scrollable_frame = ttk.Frame(canvas)
        
        # 配置Canvas
        canvas.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # 布局Canvas和滚动条
        canvas.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        # 配置容器权重
        main_container.grid_rowconfigure(0, weight=1)
        main_container.grid_columnconfigure(0, weight=1)
        
        # 创建Canvas窗口
        canvas_window = canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        
        # 配置滚动区域
        def configure_scroll_region(event=None):
            canvas.configure(scrollregion=canvas.bbox("all"))
        
        def configure_canvas_width(event):
            # 让内容框架的宽度跟随Canvas的宽度
            canvas_width = event.width
            canvas.itemconfig(canvas_window, width=canvas_width)
        
        scrollable_frame.bind("<Configure>", configure_scroll_region)
        canvas.bind("<Configure>", configure_canvas_width)
        
        # 配置鼠标滚轮
        def on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        
        def bind_mousewheel(event):
            canvas.bind_all("<MouseWheel>", on_mousewheel)
        
        def unbind_mousewheel(event):
            canvas.unbind_all("<MouseWheel>")
        
        canvas.bind('<Enter>', bind_mousewheel)
        canvas.bind('<Leave>', unbind_mousewheel)
        
        # 配置可滚动框架的网格权重
        scrollable_frame.grid_columnconfigure(1, weight=1)
        
        # 按order排序组件，然后创建
        # 为没有设置order的组件分配默认order
        for component in self.components_config:
            if component.order is None:
                if component.component_type == "log_panel":
                    component.order = 1001  # 日志面板永远在最底部
                else:
                    component.order = self._get_next_available_order()
        
        # 按order排序
        sorted_components = sorted(self.components_config, key=lambda x: x.order)
        
        # 创建组件
        for i, component_config in enumerate(sorted_components):
            ComponentFactory.create_component(
                scrollable_frame, component_config, i, 
                self.variable_manager, self.event_manager
            )
        
        # 保存引用以便后续使用
        self.main_container = main_container
        self.canvas = canvas
        self.scrollable_frame = scrollable_frame
    
    def _rebuild_gui(self):
        """重新构建GUI以显示新添加的组件"""
        if not hasattr(self, 'scrollable_frame') or getattr(self, '_rebuilding', False):
            return
        
        # 设置标志防止递归重建
        self._rebuilding = True
        
        try:
            # 清除现有组件
            for widget in self.scrollable_frame.winfo_children():
                widget.destroy()
            
            # 按order排序组件，然后重新创建
            # 为没有设置order的组件分配默认order
            for component in self.components_config:
                if component.order is None:
                    if component.component_type == "log_panel":
                        component.order = 1001  # 日志面板永远在最底部
                    else:
                        component.order = self._get_next_available_order()
            
            # 按order排序
            sorted_components = sorted(self.components_config, key=lambda x: x.order)
            
            # 重新创建组件
            for i, component_config in enumerate(sorted_components):
                ComponentFactory.create_component(
                    self.scrollable_frame, component_config, i, 
                    self.variable_manager, self.event_manager
                )
            
            # 重新设置日志处理器
            self._setup_logging()
            
            # 强制刷新界面
            self.window.update_idletasks()
            
        finally:
            # 清除重建标志
            self._rebuilding = False
    
    def _setup_logging(self):
        """设置日志处理器"""
        log_widget = self.variable_manager.get_variable('log_panel')
        if log_widget:
            self.log_handler.set_log_widget(log_widget)
            # 同时设置全局日志组件
            set_log_widget(log_widget)
    
    def log(self, message, level=logging.INFO):
        """输出日志信息"""
        self.logger.log(level, message)
    
    def log_info(self, message):
        """输出信息日志"""
        self.log(message, logging.INFO)
    
    def log_warning(self, message):
        """输出警告日志"""
        self.log(message, logging.WARNING)
    
    def log_error(self, message):
        """输出错误日志"""
        self.log(message, logging.ERROR)
    
    def auto_save_config(self):
        """自动保存配置"""
        self.save_config(silent=True)
    
    def save_config(self, silent=False):
        """保存配置"""
        from gui_utils import save_config_data
        return save_config_data(self.variable_manager, silent)
    
    def load_config(self):
        """加载配置"""
        from gui_utils import load_config_data
        load_config_data(self.variable_manager)
    
    def refresh_store_list(self):
        """刷新店铺列表（异步执行）"""
        import threading
        
        # 手动禁用刷新按钮，避免重复点击
        refresh_button = self._find_button_by_text("刷新店铺列表")
        if refresh_button:
            refresh_button.config(state='disabled', text='刷新中...')
        
        log_task_start("刷新店铺列表")
        
        # 在后台线程执行刷新
        def refresh_task():
            try:
                self._refresh_store_list_sync()
            except Exception as e:
                # 处理异常，记录错误但不关闭界面
                log_error(f"刷新店铺列表时发生异常: {str(e)}")
                # 在主线程显示错误消息
                self.window.after(0, lambda: messagebox.showerror("刷新失败", f"刷新店铺列表失败: {str(e)}"))
            finally:
                # 无论成功还是失败，都重新启用按钮
                if refresh_button:
                    self.window.after(0, lambda: refresh_button.config(state='normal', text='刷新店铺列表'))
        
        thread = threading.Thread(target=refresh_task)
        thread.daemon = True
        thread.start()
    
    def _refresh_store_list_sync(self):
        """同步刷新店铺列表的具体实现"""
        from gui_utils import validate_basic_config, setup_rpa_instance
        
        # 验证基本配置
        if not validate_basic_config(self.variable_manager):
            log_error("配置验证失败，请检查基本配置")
            return
        
        log_success("配置验证通过")
        
        # 清除现有店铺选择
        stores_frame = self.variable_manager.get_variable('stores_frame')
        stores_vars = self.variable_manager.get_variable('stores')
        
        def clear_stores():
            if stores_frame:
                for widget in stores_frame.winfo_children():
                    widget.destroy()
            if stores_vars:
                stores_vars.clear()
        
        self.window.after(0, clear_stores)
        log_step("已清除现有店铺列表")
        
        # 获取店铺列表
        try:
            # 设置RPA参数
            setup_rpa_instance(self.rpa, self.variable_manager)
            log_step("RPA参数设置完成")
            
            # 启动并获取店铺列表
            self.rpa.kill_process()
            log_step("正在启动紫鸟客户端...")
            self.rpa.start_browser()
            
            log_step("正在更新浏览器内核...")
            self.rpa.update_core()
            
            log_step("正在获取店铺列表...")
            browser_list = self.rpa.get_browser_list()
            
            if browser_list is None:
                log_error("获取店铺列表失败")
                self.window.after(0, lambda: messagebox.showerror("错误", "获取店铺列表失败"))
                return
            
            self.store_list = browser_list
            log_success(f"成功获取到 {len(browser_list)} 个店铺")
            
            # 在主线程创建店铺选择框
            def create_store_checkboxes():
                if stores_frame and stores_vars is not None:
                    # 保存完整的店铺列表用于筛选
                    self.full_store_list = browser_list.copy()
                    
                    # 创建店铺复选框
                    self._create_store_checkboxes_with_filter(stores_frame, stores_vars, browser_list)
                    
                    # 设置筛选功能
                    self._setup_store_filter()
                        
                    self.log_info(f"店铺列表界面已更新，显示 {len(browser_list)} 个店铺")
            
            self.window.after(0, create_store_checkboxes)
            
        except Exception as e:
            error_msg = f"刷新店铺列表失败: {str(e)}"
            self.log_error(error_msg)
            self.window.after(0, lambda: messagebox.showerror("错误", error_msg))
    
    def start_program(self):
        """启动程序"""
        # 检查是否已有任务在运行
        if self.task_control['is_running']:
            self.log_warning("已有任务正在运行，请先停止当前任务")
            return
        
        from gui_utils import validate_basic_config, setup_rpa_instance
        
        self.log_info("开始启动RPA任务...")
        
        # 保存配置
        self.save_config(silent=True)
        self.log_info("配置已保存")
        
        # 验证配置
        if not validate_basic_config(self.variable_manager):
            self.log_error("配置验证失败")
            return
        
        if not hasattr(self, 'store_list') or not self.store_list:
            error_msg = "请先刷新店铺列表"
            self.log_error(error_msg)
            messagebox.showerror("错误", error_msg)
            return
        
        # 获取选择的国家和店铺
        selected_countries = self._get_selected_countries()
        selected_stores = self._get_selected_stores()
        
        self.log_info(f"选择的国家: {', '.join(selected_countries) if selected_countries else '无'}")
        self.log_info(f"选择的店铺: {len(selected_stores)} 个")
        
        if not selected_countries:
            error_msg = "请至少选择一个国家"
            self.log_error(error_msg)
            messagebox.showerror("错误", error_msg)
            return
        
        if not selected_stores:
            error_msg = "请至少选择一个店铺"
            self.log_error(error_msg)
            messagebox.showerror("错误", error_msg)
            return
        
        # 验证任务要求（包括上传文件等）
        self.log_info("正在验证任务要求...")
        is_valid, error_msg = self.rpa.validate_task_requirements(self.variable_manager, selected_stores)
        if not is_valid:
            self.log_error(f"任务要求验证失败: {error_msg}")
            messagebox.showerror("错误", error_msg)
            return
        
        self.log_info("所有验证通过，准备开始任务执行")
        
        # 询问用户是否确认开始
        import tkinter.messagebox as msgbox
        confirm = msgbox.askyesno("确认开始", 
                                 f"即将开始执行任务:\n"
                                 f"• 国家数量: {len(selected_countries)}\n"
                                 f"• 店铺数量: {len(selected_stores)}\n"
                                 f"确定要开始吗？")
        
        if not confirm:
            self.log_info("用户取消了任务执行")
            return
        
        self.log_info("用户确认开始任务，开始执行...")
        
        # 重置任务控制状态
        self._reset_task_control()
        self.task_control['is_running'] = True
        
        # 禁用开始按钮，更新任务控制按钮状态
        self._disable_start_button()
        self._update_task_control_buttons("running")
        
        # 在后台线程执行任务
        thread = threading.Thread(
            target=self._run_main_program_threaded,
            args=(selected_countries, selected_stores)
        )
        thread.daemon = True
        self.task_control['current_thread'] = thread
        thread.start()
    
    def _get_selected_countries(self):
        """获取选择的国家"""
        countries_var = self.variable_manager.get_variable('countries')
        if countries_var:
            return [country for country, var in countries_var.items() if var.get()]
        return []
    
    def _get_selected_stores(self):
        """获取选择的店铺"""
        stores_vars = self.variable_manager.get_variable('stores')
        if stores_vars and hasattr(self, 'store_list'):
            selected_stores = []
            for store in self.store_list:
                store_name = store['browserName']
                if store_name in stores_vars and stores_vars[store_name].get():
                    selected_stores.append(store)
            return selected_stores
        return []
    
    def _run_main_program(self, selected_countries, selected_stores):
        """运行主程序"""
        from gui_utils import setup_rpa_instance
        
        self.log_info("开始运行主程序...")
        
        # 设置RPA参数
        setup_rpa_instance(self.rpa, self.variable_manager)
        self.log_info("RPA参数设置完成")
        
        # 设置任务控制对象
        self.rpa.set_task_control(self.task_control)
        self.log_info("任务控制设置完成")
        
        # 启动客户端
        self.rpa.kill_process()
        self.log_info("正在启动紫鸟客户端...")
        self.rpa.start_browser()
        
        self.log_info("正在更新浏览器内核...")
        self.rpa.update_core()
        
        # ========== 新逻辑：在每个店铺中执行主任务+国家遍历 ==========
        
        self.log_info(f"开始处理 {len(selected_stores)} 个店铺的任务")
        
        for i, store in enumerate(selected_stores, 1):
            # 检查是否需要停止任务
            if self.task_control['stop_event'].is_set():
                self.log_warning(f"任务已被用户停止，已处理 {i-1}/{len(selected_stores)} 个店铺")
                return
            
            # 等待暂停状态结束
            self.task_control['pause_event'].wait()
            
            # 再次检查停止状态（暂停期间可能被停止）
            if self.task_control['stop_event'].is_set():
                self.log_warning(f"任务已被用户停止，已处理 {i-1}/{len(selected_stores)} 个店铺")
                return
            
            store_name = store['browserName']
            self.log_info(f"处理店铺 {i}/{len(selected_stores)}: {store_name}")
            
            # 使用RPA实例的方法获取上传文件路径
            upload_file_path = self.rpa.get_upload_file_path(self.variable_manager, store_name)
            
            if upload_file_path:
                self.log_info(f"找到上传文件: {os.path.basename(upload_file_path)}")
                
                if self.rpa.validate_upload_file(upload_file_path, store_name):
                    self.log_info(f"开始执行店铺 {store_name} 的RPA任务...")
                    
                    # 在执行任务前再次检查停止状态
                    if self.task_control['stop_event'].is_set():
                        self.log_warning("任务已被用户停止")
                        return
                    
                    # 使用新的店铺任务执行方法（包含主任务+国家遍历）
                    self.rpa.use_one_browser_run_task(store, selected_countries, upload_file_path)
                    self.log_info(f"店铺 {store_name} 任务执行完成")
                else:
                    self.log_error(f"店铺 {store_name} 的上传文件验证失败")
            elif self.rpa.requires_upload_folder():
                self.log_warning(f"店铺 {store_name} 未找到匹配的上传文件")
            else:
                # 不需要上传文件的任务
                self.log_info(f"开始执行店铺 {store_name} 的RPA任务...")
                
                # 在执行任务前再次检查停止状态
                if self.task_control['stop_event'].is_set():
                    self.log_warning("任务已被用户停止")
                    return
                
                # 使用新的店铺任务执行方法（包含主任务+国家遍历）
                self.rpa.use_one_browser_run_task(store, selected_countries, None)
                self.log_info(f"店铺 {store_name} 任务执行完成")
        
        self.log_info("所有店铺任务处理完成！")
    

    
    def _run_main_program_threaded(self, selected_countries, selected_stores):
        """在线程中运行主程序"""
        try:
            self._run_main_program(selected_countries, selected_stores)
            
            # 检查是否被用户停止
            if self.task_control['stop_event'].is_set():
                log_task_end("RPA任务执行", False)
                log_warning("任务已被用户停止")
                
                # 在主线程中重新启用按钮并更新状态
                self.window.after_idle(lambda: self._update_task_status("已停止", "red"))
            else:
                log_task_end("RPA任务执行", True)
                log_info("任务执行完成！您可以继续使用GUI界面。")

                # 打印任务执行总结报告
                if hasattr(self.rpa, 'print_task_summary'):
                    self.rpa.print_task_summary()

                # 在主线程中重新启用按钮并更新状态
                self.window.after_idle(lambda: self._update_task_status("执行完成", "green"))
            
            # 重置任务控制状态并启用开始按钮
            self.window.after_idle(self._reset_task_control)
            self.window.after_idle(self._enable_start_button)
            self.window.after_idle(lambda: self._update_task_control_buttons("idle"))
            
        except Exception as e:
            log_error(f"任务执行异常: {str(e)}")
            log_task_end("RPA任务执行", False)
            
            # 在主线程中重新启用按钮并更新状态
            self.window.after_idle(lambda: self._update_task_status("执行失败", "red"))
            self.window.after_idle(self._reset_task_control)
            self.window.after_idle(self._enable_start_button)
            self.window.after_idle(lambda: self._update_task_control_buttons("idle"))
    
    def _disable_start_button(self):
        """禁用开始按钮"""
        button = self._find_button_by_text("开始任务")
        if button:
            button.configure(state="disabled", text="任务执行中...")
            self.start_button = button  # 保存引用
        
        # 更新任务状态
        self._update_task_status("任务执行中...", "orange")
    
    def _enable_start_button(self):
        """启用开始按钮"""
        if hasattr(self, 'start_button') and self.start_button:
            self.start_button.configure(state="normal", text="开始任务")
        
        # 更新任务状态
        self._update_task_status("就绪", "green")
    
    def _update_task_status(self, status, color):
        """更新任务状态显示"""
        try:
            status_label = self.variable_manager.get_variable('task_status')
            if status_label:
                status_label.configure(text=f"任务状态: {status}", fg=color)
        except:
            pass
    
    def _find_button_by_text(self, button_text):
        """递归查找包含指定文本的按钮"""
        def find_in_widget(widget):
            if isinstance(widget, ttk.Button):
                try:
                    if button_text in widget.cget("text"):
                        return widget
                except:
                    pass
            
            # 递归查找子组件
            try:
                for child in widget.winfo_children():
                    result = find_in_widget(child)
                    if result:
                        return result
            except:
                pass
            return None
        
        return find_in_widget(self.scrollable_frame)
    
    def run(self):
        """运行GUI"""
        self.window.mainloop()
    
    def pause_task(self):
        """暂停任务"""
        if not self.task_control['is_running']:
            self.log_warning("当前没有正在运行的任务")
            return
        
        if self.task_control['is_paused']:
            self.log_warning("任务已经处于暂停状态")
            return
        
        self.log_info("⏸️ 暂停任务...")
        self.task_control['is_paused'] = True
        self.task_control['pause_event'].clear()  # 清除事件，暂停任务
        
        self._update_task_status("已暂停", "orange")
        self._update_task_control_buttons("paused")
        
        log_info("任务已暂停，点击'继续任务'按钮可恢复执行")
    
    def resume_task(self):
        """继续任务"""
        if not self.task_control['is_running']:
            self.log_warning("当前没有正在运行的任务")
            return
        
        if not self.task_control['is_paused']:
            self.log_warning("任务当前没有暂停")
            return
        
        self.log_info("▶️ 继续任务...")
        self.task_control['is_paused'] = False
        self.task_control['pause_event'].set()  # 设置事件，继续任务
        
        self._update_task_status("任务执行中...", "blue")
        self._update_task_control_buttons("running")
        
        log_info("任务已恢复执行")
    
    def stop_task(self):
        """强制结束任务"""
        if not self.task_control['is_running']:
            self.log_warning("当前没有正在运行的任务")
            return

        self.log_info("⏹️ 强制结束任务...")

        # 立即设置停止标志
        self.task_control['stop_event'].set()
        self.task_control['is_running'] = False

        # 如果任务暂停了，先恢复以便能够检查停止标志
        if self.task_control['is_paused']:
            self.task_control['pause_event'].set()
            self.task_control['is_paused'] = False

        # 强制终止任务线程
        if hasattr(self, 'task_thread') and self.task_thread and self.task_thread.is_alive():
            try:
                import threading
                import ctypes

                # 获取线程ID
                thread_id = self.task_thread.ident
                if thread_id:
                    # 强制终止线程（Windows）
                    try:
                        ctypes.pythonapi.PyThreadState_SetAsyncExc(
                            ctypes.c_long(thread_id),
                            ctypes.py_object(SystemExit)
                        )
                        self.log_warning("已强制终止任务线程")
                    except Exception as e:
                        self.log_error(f"强制终止线程失败: {str(e)}")

                # 等待线程结束（最多2秒）
                self.task_thread.join(timeout=2)
                if self.task_thread.is_alive():
                    self.log_warning("任务线程仍在运行，但已设置停止标志")
                else:
                    self.log_success("任务线程已成功终止")

            except Exception as e:
                self.log_error(f"处理任务线程时发生错误: {str(e)}")

        # 重置任务状态
        self._reset_task_state()

        # 更新界面状态
        self._update_task_status("任务已停止", "red")
        self._update_task_control_buttons("idle")

        self.log_info("任务已强制结束")

    def _reset_task_state(self):
        """重置任务状态"""
        try:
            # 重置所有任务控制标志
            self.task_control['is_running'] = False
            self.task_control['is_paused'] = False
            self.task_control['stop_event'].clear()
            self.task_control['pause_event'].set()  # 确保不处于暂停状态

            # 清理任务线程引用
            if hasattr(self, 'task_thread'):
                self.task_thread = None

            self.log_info("任务状态已重置")

        except Exception as e:
            self.log_error(f"重置任务状态时发生错误: {str(e)}")
    
    def _update_task_control_buttons(self, state):
        """更新任务控制按钮状态"""
        try:
            pause_button = self._find_button_by_text("⏸️ 暂停任务")
            resume_button = self._find_button_by_text("▶️ 继续任务")
            stop_button = self._find_button_by_text("⏹️ 结束任务")
            
            if state == "idle":  # 空闲状态
                if pause_button:
                    pause_button.configure(state="disabled")
                if resume_button:
                    resume_button.configure(state="disabled")
                if stop_button:
                    stop_button.configure(state="disabled")
                    
            elif state == "running":  # 运行状态
                if pause_button:
                    pause_button.configure(state="normal")
                if resume_button:
                    resume_button.configure(state="disabled")
                if stop_button:
                    stop_button.configure(state="normal")
                    
            elif state == "paused":  # 暂停状态
                if pause_button:
                    pause_button.configure(state="disabled")
                if resume_button:
                    resume_button.configure(state="normal")
                if stop_button:
                    stop_button.configure(state="normal")
                    
            elif state == "stopping":  # 停止中状态
                if pause_button:
                    pause_button.configure(state="disabled")
                if resume_button:
                    resume_button.configure(state="disabled")
                if stop_button:
                    stop_button.configure(state="disabled")
                    
        except Exception as e:
            # 防止按钮状态更新失败影响主流程
            self.log_error(f"更新任务控制按钮状态失败: {str(e)}")
    
    def _reset_task_control(self):
        """重置任务控制状态"""
        self.task_control['is_running'] = False
        self.task_control['is_paused'] = False
        self.task_control['pause_event'].set()
        self.task_control['stop_event'].clear()
        self.task_control['current_thread'] = None 

    def _create_store_checkboxes_with_filter(self, stores_frame, stores_vars, browser_list):
        """创建店铺复选框（支持筛选，完全保持勾选状态）"""
        # 清空现有的复选框显示
        for widget in stores_frame.winfo_children():
            widget.destroy()
        
        # 注意：不清空 stores_vars，保持变量引用和状态
        # 只为新店铺添加变量，已存在的保持原状态
        
        # 创建筛选后显示的复选框
        for i, store in enumerate(browser_list):
            store_name = store['browserName']
            
            # 如果店铺变量不存在，创建新的
            if store_name not in stores_vars:
                var = tk.BooleanVar()
                stores_vars[store_name] = var
            else:
                # 使用已存在的变量（保持原有状态）
                var = stores_vars[store_name]
            
            # 创建复选框，连接到对应的变量
            checkbox = ttk.Checkbutton(stores_frame, text=store_name, variable=var)
            checkbox.grid(row=i//3, column=i%3, sticky=tk.W, padx=5, pady=2)
    
    def _setup_store_filter(self):
        """设置店铺筛选功能"""
        filter_var = self.variable_manager.get_variable('stores_filter')
        if filter_var:
            # 绑定筛选变化事件
            def on_filter_change(*args):
                self._filter_stores()
            
            filter_var.trace_add("write", on_filter_change)
            self.log_info("店铺筛选功能已激活")
    
    def _filter_stores(self):
        """根据筛选条件过滤店铺列表（完全保持勾选状态）"""
        filter_var = self.variable_manager.get_variable('stores_filter')
        stores_frame = self.variable_manager.get_variable('stores_frame')
        stores_vars = self.variable_manager.get_variable('stores')
        
        if not filter_var or not stores_frame or not stores_vars:
            return
        
        if not hasattr(self, 'full_store_list'):
            return
        
        filter_text = filter_var.get().lower().strip()
        
        # 根据筛选条件过滤店铺（不影响状态）
        if not filter_text:
            # 无筛选条件，显示所有店铺
            filtered_stores = self.full_store_list
        else:
            # 有筛选条件，只显示匹配的店铺
            filtered_stores = []
            for store in self.full_store_list:
                store_name = store['browserName'].lower()
                if filter_text in store_name:
                    filtered_stores.append(store)
        
        # 重新创建复选框（保持所有勾选状态）
        self._create_store_checkboxes_with_filter(stores_frame, stores_vars, filtered_stores)
        
        # 更新画布滚动区域
        canvas = self.variable_manager.get_variable('stores_canvas')
        if canvas:
            def update_scroll():
                canvas.configure(scrollregion=canvas.bbox("all"))
            self.window.after(10, update_scroll)
        
        # 显示筛选结果
        if filter_text:
            self.log_info(f"筛选结果: 找到 {len(filtered_stores)} 个匹配 '{filter_text}' 的店铺")
        else:
            self.log_info(f"显示所有 {len(filtered_stores)} 个店铺")

    def show_user_manual(self):
        """显示使用说明书"""
        manual_window = tk.Toplevel(self.window)
        manual_window.title("📖 程序使用说明书")
        manual_window.geometry("900x700")
        manual_window.transient(self.window)
        manual_window.grab_set()

        # 创建滚动文本框
        frame = ttk.Frame(manual_window)
        frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 创建文本框和滚动条
        text_widget = tk.Text(frame, wrap=tk.WORD, font=("微软雅黑", 10))
        scrollbar = ttk.Scrollbar(frame, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)

        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 使用说明书内容
        manual_content = self._get_user_manual_content()
        text_widget.insert(tk.END, manual_content)
        text_widget.config(state=tk.DISABLED)  # 设置为只读

        # 添加关闭按钮
        close_button = ttk.Button(manual_window, text="关闭", command=manual_window.destroy)
        close_button.pack(pady=10)

    def show_quick_help(self):
        """显示快速帮助"""
        help_window = tk.Toplevel(self.window)
        help_window.title("❓ 快速帮助")
        help_window.geometry("600x400")
        help_window.transient(self.window)
        help_window.grab_set()

        # 创建滚动文本框
        frame = ttk.Frame(help_window)
        frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        text_widget = tk.Text(frame, wrap=tk.WORD, font=("微软雅黑", 10))
        scrollbar = ttk.Scrollbar(frame, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)

        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 快速帮助内容
        help_content = self._get_quick_help_content()
        text_widget.insert(tk.END, help_content)
        text_widget.config(state=tk.DISABLED)

        close_button = ttk.Button(help_window, text="关闭", command=help_window.destroy)
        close_button.pack(pady=10)

    def _get_user_manual_content(self):
        """获取使用说明书内容"""
        return """
📖 紫鸟浏览器RPA自动化程序使用说明书

═══════════════════════════════════════════════════════════════

🎯 程序简介
本程序是一个基于紫鸟浏览器的Amazon店铺自动化管理工具，可以自动完成店铺的配送设置、库存管理等任务。

═══════════════════════════════════════════════════════════════

🔧 基本配置

1. 【紫鸟浏览器路径】
   - 选择紫鸟浏览器的启动程序
   - 路径示例：D:/program/SuperBrowser/SuperBrowser/starter.exe
   - 必须配置：是

2. 【WebDriver路径】
   - 选择存放ChromeDriver的文件夹
   - 程序会下载匹配的ChromeDriver版本，mac和Windows的驱动不一样，需要去紫鸟浏览器官网下载
   - 路径示例：E:/webdriver
   - 必须配置：是

3. 【用户认证】
   - 公司名称：您的紫鸟浏览器账户公司名
   - 用户名：您的紫鸟浏览器账户用户名
   - 密码：您的紫鸟浏览器账户密码
   - 必须配置：是

═══════════════════════════════════════════════════════════════

🌍 国家和店铺选择

1. 【国家选择】
   - 支持19个Amazon站点
   - 可以多选，程序会依次处理每个国家
   - 建议：首次使用选择1-2个国家进行测试

2. 【店铺选择】
   - 点击"刷新店铺列表"获取您的店铺
   - 支持多选店铺
   - 支持店铺名称筛选功能
   - 筛选方法：在筛选框中输入关键词，实时过滤店铺列表

═══════════════════════════════════════════════════════════════

📦 功能模块


1. 【退货设置模块】
   - 自动设置退货政策和地址
   - 每个店铺只执行一次
   - 仅在勾选时执行
2. 【配送设置模块】
   - 自动设置各国家的配送时间
   - 支持标准配送和加急配送设置
   - 包含特殊地区配送设置

   子模块：
   • 取消非标准配送：关闭不符合要求的配送选项
   • 特殊地区配送：设置特殊地区的配送规则，比如运输时间，运费等

3. 【亚马逊物流(FBA)设置模块】
   - 目前仅在美国站点生效
   - 包含多个子模块
   

   子模块：
   • 入库设置：配置商品入库相关设置
   • 不可售库存设置：配置不可售商品相关设置
   • 条码偏好设置：配置产品条码相关设置
   • 可售库存设置：管理产品的可售状态

═══════════════════════════════════════════════════════════════

🚀 操作流程

1. 【准备阶段】
   ① 确保紫鸟浏览器已安装
   ② 配置基本信息（路径、认证）
   ③ 选择目标国家和店铺
   ④ 勾选需要执行的功能模块

2. 【执行阶段】
   ① 点击"开始任务"
   ② 程序自动启动紫鸟浏览器
   ③ 依次处理每个店铺的每个国家
   ④ 实时查看日志了解执行进度

3. 【监控阶段】
   ① 观察日志输出
   ② 可以随时暂停/恢复任务
   ③ 遇到问题可以停止任务

═══════════════════════════════════════════════════════════════

⚠️ 注意事项

1. 【浏览器要求】
   - 必须使用紫鸟浏览器，不支持普通Chrome
   - 程序会自动管理浏览器启动和关闭
   - 不支持无头模式（浏览器会显示窗口）

2. 【网络要求】
   - 确保网络连接稳定
   - 紫鸟浏览器需要连接到代理服务器
   - 建议在网络稳定的环境下运行

3. 【账户安全】
   - 程序会保存您的登录信息到本地配置文件
   - 请确保计算机安全
   - 不要在公共计算机上使用

4. 【执行时间】
   - 每个店铺每个国家大约需要2-5分钟
   - 总时间 = 店铺数量 × 国家数量 × 平均时间
   - 建议分批处理大量店铺

═══════════════════════════════════════════════════════════════

🔧 故障排除

1. 【浏览器启动失败】
   - 检查紫鸟浏览器路径是否正确
   - 确认紫鸟浏览器可以正常启动
   - 检查网络连接

2. 【店铺列表为空】
   - 检查用户认证信息是否正确
   - 确认账户有店铺权限
   - 尝试重新刷新店铺列表

3. 【任务执行失败】
   - 查看日志了解具体错误
   - 检查网络连接是否稳定
   - 确认店铺状态是否正常

4. 【ChromeDriver问题】
   - 程序会自动下载匹配的ChromeDriver
   - 如果下载失败，请检查网络连接
   - 可以手动下载ChromeDriver到指定文件夹

═══════════════════════════════════════════════════════════════

📞 技术支持

如果遇到问题，请：
1. 查看程序日志了解详细错误信息
2. 检查配置是否正确
3. 确认网络和浏览器状态
4. 联系技术支持并提供日志信息

═══════════════════════════════════════════════════════════════

版本信息：v2.0
更新日期：2025年
"""

    def _get_quick_help_content(self):
        """获取快速帮助内容"""
        return """
❓ 快速帮助

═══════════════════════════════════════════════════════════════

🚀 快速开始

1. 配置基本信息
   • 紫鸟浏览器路径：选择starter.exe文件
   • WebDriver路径：选择存放驱动的文件夹
   • 用户认证：输入紫鸟账户信息

2. 选择目标
   • 勾选要处理的国家
   • 点击"刷新店铺列表"
   • 勾选要处理的店铺

3. 选择功能
   • 勾选需要设置的模块，如"配送设置模块"
   • 根据需要勾选子模块

4. 开始任务
   • 点击"开始任务"
   • 观察日志输出

═══════════════════════════════════════════════════════════════

🔧 常用操作

• 筛选店铺：在店铺筛选框中输入关键词
• 暂停任务：点击"暂停任务"按钮
• 恢复任务：点击"恢复任务"按钮
• 停止任务：点击"停止任务"按钮
• 查看日志：在下方日志区域查看实时信息

═══════════════════════════════════════════════════════════════

⚠️ 重要提醒

• 首次使用建议选择1-2个店铺进行测试
• 确保网络连接稳定
• 不要在任务执行期间关闭程序
• 遇到问题请查看日志信息

═══════════════════════════════════════════════════════════════

📖 详细说明

点击"使用说明书"查看完整的使用指南
"""