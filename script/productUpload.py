import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from gui_framework import ConfigurableGUI, ComponentConfig
from ziniao_rpa_base import BaseZiniaoRPA
from global_logger import log_info, log_warning, log_error, log_success, log_step, log_task_start, log_task_end, log_separator


class ProductUpload(BaseZiniaoRPA):
    def get_task_name(self):
        return "产品上传任务"
    
    def requires_upload_folder(self):
        """
        此任务需要上传文件夹
        """
        return True
    
    def validate_task_requirements(self, variable_manager, selected_stores):
        """
        自定义验证逻辑 - 检查上传文件并添加额外验证
        """
        # 首先调用父类的默认验证（检查上传文件夹和文件）
        is_valid, error_msg = self._validate_upload_files_default(variable_manager, selected_stores)
        if not is_valid:
            return False, error_msg
        
        # 添加额外的自定义验证
        upload_folder_var = variable_manager.get_variable('upload_folder')
        upload_folder = upload_folder_var.get()
        
        # 检查每个文件的格式
        invalid_files = []
        for store in selected_stores:
            store_name = store['browserName']
            matching_files = [f for f in __import__('os').listdir(upload_folder) if f.startswith(store_name)]
            if matching_files:
                file_path = __import__('os').path.join(upload_folder, matching_files[0])
                # 检查文件扩展名
                if not matching_files[0].lower().endswith(('.csv', '.xlsx', '.xls')):
                    invalid_files.append(f"{store_name}: {matching_files[0]} (不支持的格式)")
        
        if invalid_files:
            error_msg = f"以下文件格式不正确，仅支持CSV和Excel文件：\n{chr(10).join(invalid_files[:5])}"
            if len(invalid_files) > 5:
                error_msg += f"\n... 还有 {len(invalid_files) - 5} 个文件格式错误"
            return False, error_msg
        
        log_info("产品上传任务验证通过")
        return True, None

    def requires_country_iteration(self):
        """
        产品上传任务需要国家遍历
        """
        return True
    
    def execute_main_task(self, driver, upload_file_path, store_info):
        """执行主要任务（产品上传前的准备工作）"""
        log_task_start("执行产品上传主要任务")
        
        try:
            # 在商铺主页执行产品上传前的准备工作
            log_step("正在进行产品上传前的准备工作...")
            
            # 例如：检查产品类目、验证权限等
            log_step("正在验证产品上传权限...")
            log_step("正在检查产品类目配置...")
            
            # 模拟一些操作
            import time
            time.sleep(2)
            
            log_success("产品上传主要任务（准备工作）完成")
            return True
            
        except Exception as e:
            log_error(f"执行产品上传主要任务时发生错误: {str(e)}")
            return False

    def execute_country_task(self, driver, country, upload_file_path, store_info):
        # 实现产品上传的具体业务逻辑
        log_task_start(f"在 {country} 执行产品上传任务")
        
        try:
            log_step(f"正在处理上传文件: {upload_file_path}")
            
            # 示例：解析上传文件
            log_step("正在解析产品数据...")
            
            # 这里添加您的具体业务逻辑
            log_step("正在上传产品到平台...")
            
            # 模拟一些操作
            import time
            time.sleep(3)
            
            log_success(f"{country} 产品上传完成")
            return True
            
        except Exception as e:
            log_error(f"执行产品上传时发生错误: {str(e)}")
            return False


class ProductUploadGUI(ConfigurableGUI):
    def __init__(self, rpa_instance):
        super().__init__(rpa_instance)
    
    def _pre_gui_setup(self):
        """在GUI构建前进行设置"""
        # 添加自定义命令
        self.add_custom_command("preview_files", self.preview_upload_files)
        self.add_custom_command("validate_format", self.validate_file_formats)
        
        # 添加产品上传特有的功能区块
        self.add_section(
            "📦 产品上传功能",
            [
                ComponentConfig("label", "上传文件预览和验证:", 
                              font=("Arial", 12, "bold"), 
                              fg="blue"),
                ComponentConfig("button_group", "",
                                buttons=[
                                    {"text": "📋 预览上传文件", "command": "preview_files"},
                                    {"text": "✅ 验证文件格式", "command": "validate_format"}
                                ])
            ],
            base_order=1  # 设置在最前面显示
        )

    def preview_upload_files(self):
        """预览上传文件"""
        log_separator("文件预览")
        log_info("开始预览上传文件...")
        
        upload_folder_var = self.variable_manager.get_variable('upload_folder')
        if not upload_folder_var or not upload_folder_var.get():
            log_error("请先选择上传文件文件夹")
            return
        
        upload_folder = upload_folder_var.get()
        import os
        
        try:
            files = os.listdir(upload_folder)
            if not files:
                log_warning("上传文件夹为空")
                return
            
            log_info(f"文件夹中共有 {len(files)} 个文件:")
            for i, file in enumerate(files[:10], 1):  # 只显示前10个
                file_size = os.path.getsize(os.path.join(upload_folder, file))
                log_info(f"  {i}. {file} ({file_size} 字节)")
            
            if len(files) > 10:
                log_info(f"  ... 还有 {len(files) - 10} 个文件")
                
        except Exception as e:
            log_error(f"预览文件失败: {str(e)}")

    def validate_file_formats(self):
        """验证文件格式"""
        log_separator("格式验证")
        log_info("开始验证文件格式...")
        
        upload_folder_var = self.variable_manager.get_variable('upload_folder')
        if not upload_folder_var or not upload_folder_var.get():
            log_error("请先选择上传文件文件夹")
            return
        
        upload_folder = upload_folder_var.get()
        import os
        
        try:
            files = os.listdir(upload_folder)
            valid_files = []
            invalid_files = []
            
            for file in files:
                if file.lower().endswith(('.csv', '.xlsx', '.xls')):
                    valid_files.append(file)
                else:
                    invalid_files.append(file)
            
            log_info(f"格式验证结果:")
            log_success(f"  ✅ 有效文件: {len(valid_files)} 个")
            if valid_files:
                for file in valid_files[:5]:
                    log_info(f"    - {file}")
                if len(valid_files) > 5:
                    log_info(f"    ... 还有 {len(valid_files) - 5} 个")
            
            if invalid_files:
                log_warning(f"  ❌ 无效文件: {len(invalid_files)} 个")
                for file in invalid_files[:5]:
                    log_warning(f"    - {file}")
                if len(invalid_files) > 5:
                    log_warning(f"    ... 还有 {len(invalid_files) - 5} 个")
            else:
                log_success("所有文件格式验证通过！")
                
        except Exception as e:
            log_error(f"验证文件格式失败: {str(e)}")


if __name__ == "__main__":
    # 创建RPA实例和GUI应用
    rpa = ProductUpload()
    app = ProductUploadGUI(rpa)
    
    # 启动应用
    log_info("启动产品上传应用...")
    app.run() 