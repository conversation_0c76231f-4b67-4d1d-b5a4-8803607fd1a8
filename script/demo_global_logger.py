"""
演示全局日志功能的示例脚本

这个脚本展示了如何在script包中使用全局日志功能
所有日志输出都会显示在前端GUI界面中
"""

from gui_framework import ConfigurableGUI, ComponentConfig
from ziniao_rpa_base import BaseZiniaoRPA
from global_logger import (
    log_info, log_warning, log_error, log_success, log_step, 
    log_task_start, log_task_end, log_separator, log_debug
)
import time


class DemoRPA(BaseZiniaoRPA):
    """演示RPA类"""
    
    def get_task_name(self):
        return "全局日志演示"

    def execute_country_task(self, driver, country, upload_file_path, store_info):
        """演示在国家任务中使用全局日志"""
        log_task_start(f"在 {country} 执行演示任务")
        
        try:
            # 模拟各种操作步骤
            log_step("步骤1: 初始化浏览器环境")
            time.sleep(1)
            
            log_step("步骤2: 登录账户")
            time.sleep(1)
            
            log_step("步骤3: 导航到目标页面")
            time.sleep(1)
            
            log_step("步骤4: 处理数据")
            time.sleep(1)
            
            # 模拟一个警告
            log_warning("检测到网络延迟，正在重试...")
            time.sleep(1)
            
            log_step("步骤5: 保存结果")
            time.sleep(1)
            
            log_success(f"{country} 演示任务执行完成")
            log_task_end(f"在 {country} 执行演示任务", True)
            return True
            
        except Exception as e:
            log_error(f"执行演示任务时发生错误: {str(e)}")
            log_task_end(f"在 {country} 执行演示任务", False)
            return False


class DemoGUI(ConfigurableGUI):
    """演示GUI类"""
    
    def __init__(self, rpa_instance):
        super().__init__(rpa_instance)
        
        # 添加演示功能区块
        self.add_section(
            "=== 全局日志演示功能 ===",
            [
                ComponentConfig("button_group", "", 
                              buttons=[
                                  {"text": "基础日志演示", "command": "demo_basic_logs"},
                                  {"text": "任务流程演示", "command": "demo_task_flow"},
                                  {"text": "错误处理演示", "command": "demo_error_handling"},
                                  {"text": "清空日志", "command": "clear_logs"}
                              ]),
            ],
            base_order=5
        )
        
        # 注册自定义命令
        self.add_custom_commands({
            "demo_basic_logs": self.demo_basic_logs,
            "demo_task_flow": self.demo_task_flow,
            "demo_error_handling": self.demo_error_handling,
            "clear_logs": self.clear_logs
        })

    def demo_basic_logs(self):
        """演示基础日志功能"""
        log_separator("基础日志演示")
        log_info("这是一条信息日志")
        log_warning("这是一条警告日志")
        log_error("这是一条错误日志")
        log_success("这是一条成功日志")
        log_step("这是一条步骤日志")
        log_debug("这是一条调试日志")
        log_info("基础日志演示完成！")

    def demo_task_flow(self):
        """演示任务流程日志"""
        log_separator("任务流程演示")
        
        # 模拟一个完整的任务流程
        log_task_start("数据处理任务")
        
        log_step("正在读取配置文件...")
        time.sleep(0.5)
        
        log_step("正在连接数据库...")
        time.sleep(0.5)
        
        log_step("正在处理数据...")
        time.sleep(1)
        
        log_warning("发现重复数据，正在去重...")
        time.sleep(0.5)
        
        log_step("正在保存结果...")
        time.sleep(0.5)
        
        log_success("数据处理完成，共处理 1000 条记录")
        log_task_end("数据处理任务", True)

    def demo_error_handling(self):
        """演示错误处理日志"""
        log_separator("错误处理演示")
        
        log_task_start("文件上传任务")
        
        try:
            log_step("正在检查文件...")
            time.sleep(0.5)
            
            log_step("正在上传文件...")
            time.sleep(1)
            
            # 模拟一个错误
            raise Exception("网络连接超时")
            
        except Exception as e:
            log_error(f"文件上传失败: {str(e)}")
            log_step("正在尝试重新上传...")
            time.sleep(1)
            log_success("重新上传成功")
            log_task_end("文件上传任务", True)

    def clear_logs(self):
        """清空日志"""
        log_widget = self.variable_manager.get_variable('log_panel')
        if log_widget:
            log_widget.delete(1.0, 'end')
            log_info("日志已清空")


if __name__ == "__main__":
    # 创建RPA实例和GUI应用
    rpa = DemoRPA()
    app = DemoGUI(rpa)
    
    # 启动应用
    log_info("启动全局日志演示应用...")
    log_info("点击上方按钮来测试不同的日志功能")
    app.run() 