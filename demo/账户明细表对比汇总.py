import pandas as pd
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def compare_and_merge_excel_files(file1_path, file2_path, output_path=None):
    """
    对比两个Excel文件的账户明细表，合并店铺信息并生成汇总报告
    
    Args:
        file1_path (str): 第一个Excel文件路径
        file2_path (str): 第二个Excel文件路径  
        output_path (str): 输出文件路径，如果为None则自动生成
    
    Returns:
        str: 输出文件路径
    """
    try:
        logger.info("开始读取Excel文件...")
        
        # 读取Excel文件
        df1 = pd.read_excel(file1_path)
        df2 = pd.read_excel(file2_path)
        
        logger.info(f"表1数据行数: {len(df1)}")
        logger.info(f"表2数据行数: {len(df2)}")
        
        # 检查必需的列是否存在
        required_columns = ['交易流水号', '店铺']
        
        for col in required_columns:
            if col not in df1.columns:
                raise ValueError(f"表1缺少必需的列: {col}")
            if col not in df2.columns:
                raise ValueError(f"表2缺少必需的列: {col}")
        
        # 检查是否存在交易类型列
        has_transaction_type = '交易类型' in df1.columns
        if has_transaction_type:
            logger.info("检测到交易类型列，将启用智能店铺查找功能")
        else:
            logger.warning("未检测到交易类型列，将跳过智能店铺查找功能")
        
        # 检查是否存在交易单号列（用于智能查找）
        has_transaction_order_id = '交易单号' in df1.columns
        if has_transaction_type and not has_transaction_order_id:
            logger.warning("检测到交易类型列但缺少交易单号列，智能查找功能可能无法正常工作")
        elif has_transaction_type and has_transaction_order_id:
            logger.info("检测到交易类型列和交易单号列，智能查找功能已启用")
        
        # 创建结果DataFrame，以df1为基础
        result_df = df1.copy()
        
        # 添加汇总备注列
        result_df['汇总备注'] = ''
        
        # 记录不一致的数据
        inconsistent_records = []
        update_count = 0
        smart_find_count = 0
        smart_find_attempts = 0  # 记录智能查找尝试次数
        
        logger.info("开始对比和合并数据...")
        
        # 性能优化：预先创建索引
        logger.info("正在创建数据索引以加速处理...")
        df2_index = df2.set_index('交易流水号')
        
        # 遍历表1的每一行
        for idx, row1 in result_df.iterrows():
            if idx % 1000 == 0:  # 每处理1000行输出一次进度
                logger.info(f"处理进度: {idx}/{len(result_df)} ({idx/len(result_df)*100:.1f}%)")
                
            transaction_id = row1['交易流水号']
            shop1 = row1['店铺']
            
            # 在表2中查找对应的交易流水号（使用索引加速）
            try:
                matching_rows = df2_index.loc[[transaction_id]]
            except KeyError:
                matching_rows = pd.DataFrame()
            
            if not matching_rows.empty:
                # 获取表2中对应的店铺信息
                shop2 = matching_rows.iloc[0]['店铺']
                
                # 检查店铺值是否有效（不是空值、NaN或#N/A）
                def is_valid_shop_value(value):
                    if pd.isna(value):
                        return False
                    if value == '' or str(value).strip() == '':
                        return False
                    if str(value).strip().upper() == '#N/A':
                        return False
                    return True
                
                # 表1店铺无效的情况
                if not is_valid_shop_value(shop1):
                    # 先检查表2相同交易流水号的店铺是否有效
                    if is_valid_shop_value(shop2):
                        # 表2有效，使用表2的店铺信息
                        result_df.at[idx, '店铺'] = shop2
                        update_count += 1
                        logger.debug(f"交易流水号 {transaction_id}: 从表2补充店铺信息 - {shop2}")
                    else:
                        # 表2也无效，使用交易单号智能查找
                        logger.debug(f"交易流水号 {transaction_id}: 表1和表2店铺都无效，准备智能查找")
                        smart_find_attempts += 1
                        if has_transaction_type and has_transaction_order_id:
                            # 获取当前行的交易类型和交易单号
                            transaction_type = row1.get('交易类型', '')
                            current_transaction_order_id = row1.get('交易单号', '')
                            
                            # 检查是否为订单或采购单
                            if transaction_type in ['订单', '采购单']:
                                logger.debug(f"交易流水号 {transaction_id}: 开始智能查找 - 交易类型: {transaction_type}, 交易单号: {current_transaction_order_id}")
                                
                                # 在表1中查找所有订单或采购单类型且交易单号一致的有效店铺记录
                                # 先查找所有相同交易单号的记录
                                same_order_id_records = df1[df1['交易单号'] == current_transaction_order_id]
                                logger.debug(f"交易流水号 {transaction_id}: 找到 {len(same_order_id_records)} 条相同交易单号的记录")
                                
                                # 再筛选出订单/采购单类型且有有效店铺的记录
                                order_purchase_records = same_order_id_records[
                                    (same_order_id_records['交易类型'].isin(['订单', '采购单'])) & 
                                    (same_order_id_records['店铺'].apply(is_valid_shop_value))
                                ]
                                
                                logger.debug(f"交易流水号 {transaction_id}: 在表1中找到 {len(order_purchase_records)} 条匹配记录")
                                
                                if not order_purchase_records.empty:
                                    # 获取第一个有效店铺
                                    found_shop = order_purchase_records.iloc[0]['店铺']
                                    result_df.at[idx, '店铺'] = found_shop
                                    
                                    # 记录使用的交易单号
                                    used_transaction_ids = order_purchase_records['交易单号'].tolist()
                                    if len(used_transaction_ids) > 1:
                                        remark = f"智能查找: 采用订单/采购单数据店铺信息，匹配到{len(used_transaction_ids)}个交易单号: {', '.join(map(str, used_transaction_ids))}"
                                    else:
                                        remark = f"智能查找: 采用订单/采购单数据店铺信息，交易单号: {used_transaction_ids[0]}"
                                    
                                    result_df.at[idx, '汇总备注'] = remark
                                    smart_find_count += 1
                                    logger.info(f"交易流水号 {transaction_id}: 智能查找到店铺信息 - {found_shop}")
                                else:
                                    # 在表2中也尝试查找
                                    logger.debug(f"交易流水号 {transaction_id}: 表1未找到匹配记录，尝试在表2中查找")
                                    # 先查找所有相同交易单号的记录
                                    same_order_id_records_df2 = df2[df2['交易单号'] == current_transaction_order_id]
                                    logger.debug(f"交易流水号 {transaction_id}: 在表2中找到 {len(same_order_id_records_df2)} 条相同交易单号的记录")
                                    
                                    # 再筛选出订单/采购单类型且有有效店铺的记录
                                    order_purchase_records_df2 = same_order_id_records_df2[
                                        (same_order_id_records_df2['交易类型'].isin(['订单', '采购单'])) & 
                                        (same_order_id_records_df2['店铺'].apply(is_valid_shop_value))
                                    ]
                                    
                                    logger.debug(f"交易流水号 {transaction_id}: 在表2中找到 {len(order_purchase_records_df2)} 条匹配记录")
                                    
                                    if not order_purchase_records_df2.empty:
                                        found_shop = order_purchase_records_df2.iloc[0]['店铺']
                                        result_df.at[idx, '店铺'] = found_shop
                                        
                                        used_transaction_ids = order_purchase_records_df2['交易单号'].tolist()
                                        if len(used_transaction_ids) > 1:
                                            remark = f"智能查找(表2): 采用订单/采购单数据店铺信息，匹配到{len(used_transaction_ids)}个交易单号: {', '.join(map(str, used_transaction_ids))}"
                                        else:
                                            remark = f"智能查找(表2): 采用订单/采购单数据店铺信息，交易单号: {used_transaction_ids[0]}"
                                        
                                        result_df.at[idx, '汇总备注'] = remark
                                        smart_find_count += 1
                                        logger.info(f"交易流水号 {transaction_id}: 从表2智能查找到店铺信息 - {found_shop}")
                                    else:
                                        logger.debug(f"交易流水号 {transaction_id}: 表1和表2都未找到匹配的店铺信息")
                            else:
                                logger.debug(f"交易流水号 {transaction_id}: 交易类型不是订单或采购单: {transaction_type}")
                
                # 表1店铺有效的情况
                elif is_valid_shop_value(shop1):
                    # 检查表2相同交易流水号的店铺是否有效
                    if is_valid_shop_value(shop2):
                        # 表2也有效，对比是否一致
                        if shop1 != shop2:
                            # 不一致，记录到不一致记录，但使用表1的信息
                            inconsistent_record = {
                                '交易流水号': transaction_id,
                                '表1店铺': shop1,
                                '表2店铺': shop2,
                                '备注': '店铺信息不一致'
                            }
                            inconsistent_records.append(inconsistent_record)
                            logger.warning(f"交易流水号 {transaction_id}: 店铺信息不一致 - 表1: {shop1}, 表2: {shop2}")
                        # 如果一致，保持表1信息，无需额外处理
                    # 如果表2无效，保持表1信息，无需额外处理
        
        # 创建不一致记录的DataFrame
        inconsistent_df = pd.DataFrame(inconsistent_records)
        
        # 生成输出文件路径
        if output_path is None:
            output_path = Path(file1_path).parent / "账户明细表对比汇总结果.xlsx"
        
        logger.info("开始写入结果文件...")
        
        # 写入Excel文件
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            # 写入合并后的数据到Sheet1
            result_df.to_excel(writer, sheet_name='合并结果', index=False)
            
            # 写入不一致记录到Sheet2
            if not inconsistent_df.empty:
                inconsistent_df.to_excel(writer, sheet_name='不一致记录', index=False)
            else:
                # 如果没有不一致记录，创建一个空的sheet2
                empty_df = pd.DataFrame(columns=['交易流水号', '表1店铺', '表2店铺', '备注'])
                empty_df.to_excel(writer, sheet_name='不一致记录', index=False)
        
        # 美化Excel格式
        _format_excel_output(output_path, len(result_df), len(inconsistent_df))
        
        # 生成统计信息
        logger.info("=" * 50)
        logger.info("对比汇总完成！")
        logger.info(f"总数据行数: {len(result_df)}")
        logger.info(f"从表2补充店铺信息的记录数: {update_count}")
        logger.info(f"智能查找尝试次数: {smart_find_attempts}")
        logger.info(f"智能查找补充店铺信息的记录数: {smart_find_count}")
        logger.info(f"店铺信息不一致的记录数: {len(inconsistent_df)}")
        logger.info(f"结果文件已保存至: {output_path}")
        logger.info("=" * 50)
        
        return str(output_path)
        
    except Exception as e:
        logger.error(f"处理Excel文件时发生错误: {str(e)}")
        raise


def _format_excel_output(file_path, main_sheet_rows, inconsistent_rows):
    """
    美化Excel输出格式
    """
    try:
        wb = openpyxl.load_workbook(file_path)
        
        # 格式化合并结果sheet
        if '合并结果' in wb.sheetnames:
            ws1 = wb['合并结果']
            _format_worksheet(ws1, "合并结果", main_sheet_rows)
        
        # 格式化不一致记录sheet
        if '不一致记录' in wb.sheetnames:
            ws2 = wb['不一致记录']
            _format_worksheet(ws2, "不一致记录", inconsistent_rows)
        
        wb.save(file_path)
        logger.info("Excel格式美化完成")
        
    except Exception as e:
        logger.warning(f"Excel格式美化失败: {str(e)}")


def _format_worksheet(worksheet, sheet_name, row_count):
    """
    格式化工作表
    """
    # 设置标题行样式
    header_font = Font(name='微软雅黑', size=11, bold=True, color='FFFFFF')
    header_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
    header_alignment = Alignment(horizontal='center', vertical='center')
    
    # 应用标题行格式
    for cell in worksheet[1]:
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
    
    # 设置数据行样式
    data_font = Font(name='微软雅黑', size=10)
    data_alignment = Alignment(horizontal='left', vertical='center')
    
    # 应用数据行格式
    for row in worksheet.iter_rows(min_row=2, max_row=row_count + 1):
        for cell in row:
            cell.font = data_font
            cell.alignment = data_alignment
    
    # 自动调整列宽
    for column in worksheet.columns:
        max_length = 0
        column_letter = column[0].column_letter
        
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        
        adjusted_width = min(max_length + 2, 50)
        worksheet.column_dimensions[column_letter].width = adjusted_width


def main():
    """
    主函数 - 示例用法
    """
    try:
        # 示例文件路径（请根据实际情况修改）
        file1_path = r'C:\Users\<USER>\Desktop\对比\trade_detail_1753259336970.xlsx'
        file2_path = r'C:\Users\<USER>\Desktop\对比\liehao.xlsx'
        
        # 快速测试：检查前几行数据
        logger.info("=== 快速数据检查 ===")
        df1_test = pd.read_excel(file1_path, nrows=5)
        df2_test = pd.read_excel(file2_path, nrows=5)
        
        logger.info("表1前5行数据:")
        logger.info(f"列名: {list(df1_test.columns)}")
        for idx, row in df1_test.iterrows():
            logger.info(f"行{idx}: 交易流水号={row.get('交易流水号', 'N/A')}, 交易单号={row.get('交易单号', 'N/A')}, 交易类型={row.get('交易类型', 'N/A')}, 店铺={row.get('店铺', 'N/A')}")
        
        logger.info("表2前5行数据:")
        logger.info(f"列名: {list(df2_test.columns)}")
        for idx, row in df2_test.iterrows():
            logger.info(f"行{idx}: 交易流水号={row.get('交易流水号', 'N/A')}, 交易单号={row.get('交易单号', 'N/A')}, 交易类型={row.get('交易类型', 'N/A')}, 店铺={row.get('店铺', 'N/A')}")
        
        logger.info("=== 开始正式处理 ===")
        
        # 检查文件是否存在
        if not Path(file1_path).exists():
            logger.error(f"文件不存在: {file1_path}")
            return
        
        if not Path(file2_path).exists():
            logger.error(f"文件不存在: {file2_path}")
            return
        
        # 执行对比和合并
        output_file = compare_and_merge_excel_files(file1_path, file2_path)
        
        print(f"\n处理完成！结果文件: {output_file}")
        
    except KeyboardInterrupt:
        logger.info("用户取消操作")
    except Exception as e:
        logger.error(f"程序执行失败: {str(e)}")


if __name__ == "__main__":
    main()
